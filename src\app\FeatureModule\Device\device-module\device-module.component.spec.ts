import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DeviceSearchRequest } from 'src/app/model/device/deviceSearchRequest.model';
import { DeviceOperationService } from '../DeviceService/Device-Operation/device-operation.service';
import { DeviceModuleComponent } from './device-module.component';

describe('DeviceModuleComponent', () => {
  let component: DeviceModuleComponent;
  let fixture: ComponentFixture<DeviceModuleComponent>;
  let deviceOperationServiceSpy: jasmine.SpyObj<DeviceOperationService>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('DeviceOperationService', ['callRefreshPageSubject']);

    await TestBed.configureTestingModule({
      declarations: [DeviceModuleComponent],
      providers: [
        { provide: DeviceOperationService, useValue: spy }
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeviceModuleComponent);
    component = fixture.componentInstance;
    deviceOperationServiceSpy = TestBed.inject(DeviceOperationService) as jasmine.SpyObj<DeviceOperationService>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Variable Update Methods', () => {
    it('should update isFilterComponentInitWithApicall when called from child', () => {
      // Arrange
      const testValue = false;
      component.isFilterComponentInitWithApicall = true;

      // Act
      component.updateIsFilterComponentInitWithApicall(testValue);

      // Assert
      expect(component.isFilterComponentInitWithApicall).toBe(testValue);
    });

    it('should update listPageRefreshForbackToDetailPage when called from child', () => {
      // Arrange
      const testValue = true;
      component.listPageRefreshForbackToDetailPage = false;

      // Act
      component.updateListPageRefreshForbackToDetailPage(testValue);

      // Assert
      expect(component.listPageRefreshForbackToDetailPage).toBe(testValue);
    });

    it('should update deviceSearchRequestBody when called from child', () => {
      // Arrange
      const testValue = new DeviceSearchRequest(['test'], 'active', null, null, null, null, null, null, null, null, null);
      component.deviceSearchRequestBody = null;

      // Act
      component.updateDeviceSearchRequestBody(testValue);

      // Assert
      expect(component.deviceSearchRequestBody).toBe(testValue);
    });

    it('should update isFilterHidden when called from child', () => {
      // Arrange
      const testValue = true;
      component.isFilterHidden = false;

      // Act
      component.updateIsFilterHidden(testValue);

      // Assert
      expect(component.isFilterHidden).toBe(testValue);
    });
  });

  describe('showDevice', () => {
    it('should show device listing page and hide detail page', () => {
      // Arrange
      component.isDeviceListingPageDisplay = false;
      component.isDeviceDetailPageDisplay = true;

      // Act
      component.showDevice();

      // Assert
      expect(component.isDeviceListingPageDisplay).toBe(true);
      expect(component.isDeviceDetailPageDisplay).toBe(false);
    });
  });

  describe('getDeviceId', () => {
    it('should set deviceIdInput when called', () => {
      // Arrange
      const testDeviceId = 123;

      // Act
      component.getDeviceId(testDeviceId);

      // Assert
      expect(component.deviceIdInput).toBe(testDeviceId);
    });
  });
});
