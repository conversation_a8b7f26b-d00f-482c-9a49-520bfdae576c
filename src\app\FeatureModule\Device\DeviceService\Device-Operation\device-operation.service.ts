import { Injectable } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Subject, firstValueFrom } from 'rxjs';
import { DEVICE_ALREADY_CLIENT, DEVICE_ALREADY_DEMO, DEVICE_ALREADY_TEST, DEVICE_CONVERT_TO_CLIENT, DEVICE_CONVERT_TO_DEMO, DEVICE_CONVERT_TO_TEST, DeviceDetailResource, DeviceListResource, Device_Select_Message } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { AssignSelectedReleaseVersionRequest } from 'src/app/model/device/AssignSelectedReleaseVersionRequest.model';
import { DeviceExportCSVSearchRequest } from 'src/app/model/device/DeviceExportCSVSearchRequest.model';
import { DeviceFilterAction } from 'src/app/model/device/DeviceFilterAction.model';
import { DeviceSearchRequest } from 'src/app/model/device/deviceSearchRequest.model';
import { TransferProductDetails } from 'src/app/model/device/TransferProductDetails.model';
import { ReleaseVersionRequest } from 'src/app/model/release-version-request.model';
import { DeviceService } from '../../../../shared/device.service';
import { deviceTypesEnum } from '../../../../shared/enum/deviceTypesEnum.enum';
import { ExceptionHandlingService } from '../../../../shared/ExceptionHandling.service';
import { CommonsService } from '../../../../shared/util/commons.service';
import { DownloadService } from '../../../../shared/util/download.service';
import { ModuleValidationServiceService } from '../../../../shared/util/module-validation-service.service';
import { CountryCacheService } from '../../../../shared/Service/CacheService/countrycache.service';
import { SalesOrderApiCallService } from '../../../../shared/Service/SalesOrderService/sales-order-api-call.service';
import { ReleaseVersionsResult } from '../../../../shared/Interface/ReleaseVersionsResult.model';
import { DeviceDetailResult } from '../../../../shared/Interface/DeviceDetailResult.model';
import { DeviceListResult } from '../../../../shared/Interface/DeviceListResult.model';
import { DeviceListProcessedResult } from '../../../../shared/Interface/DeviceListProcessedResult.model';

/**
* Device Filter Service for communication between device filter and listing components
* Includes caching functionality to avoid unnecessary API calls on filter show/hide
*
* <AUTHOR>
*/
@Injectable({
  providedIn: 'root'
})
export class DeviceOperationService {

  constructor(
    private deviceService: DeviceService,
    private salesOrderApiCallService: SalesOrderApiCallService,
    private countryCacheService: CountryCacheService,
    private commonsService: CommonsService,
    private exceptionHandlingService: ExceptionHandlingService,
    private toastrService: ToastrService,
    private downloadService: DownloadService,
    private moduleValidationService: ModuleValidationServiceService
  ) { }

  /**
  * Cached filter data to avoid API calls on filter show/hide 
  * <AUTHOR>
  */
  private packageVersionList: string[] = [];
  private salesOrderNumberList: string[] = [];
  private countryList: CountryListResponse[] = [];


  //Device list filter
  private deviceListFilterRequestParameterSubject = new Subject<DeviceFilterAction>();

  //Refresh Device List
  private deviceListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  /**
  * Get Device List Filter Request Parameter Subject
  * Used by device listing component to subscribe to filter changes
  * <AUTHOR>
  * @returns Subject<DeviceFilterAction>
  */
  public getDeviceListFilterRequestParameterSubject(): Subject<DeviceFilterAction> {
    return this.deviceListFilterRequestParameterSubject;
  }

  /**
  * Call Device List Filter Request Parameter Subject
  * Used by device filter component to emit filter changes
  * <AUTHOR>
  * @param deviceFilterAction - The filter action containing search parameters
  */
  public callDeviceListFilterRequestParameterSubject(deviceFilterAction: DeviceFilterAction): void {
    this.deviceListFilterRequestParameterSubject.next(deviceFilterAction);
  }

  /**
  * Get Device List Refresh Subject
  * Used by device filter component to subscribe to refresh events
  * <AUTHOR>
  * @returns Subject<ListingPageReloadSubjectParameter>
  */
  public getDeviceListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.deviceListRefreshSubject;
  }

  /**
  * This function call the subject for reload the page data
  * Note : (DeviceListResource) -> Filter page subject call -> Listing page subject call
  * clear all filter after page data Reload
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter
  * @param resourceName
  * @param isFilterHidden
  * @param deviceSearchRequestBodyApply
  */
  public callRefreshPageSubject(
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter,
    resourceName: string,
    isFilterHidden: boolean,
    deviceSearchRequestBodyApply: DeviceSearchRequest
  ): void {
    if (resourceName == DeviceListResource) {
      if (isFilterHidden) {
        let deviceSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
        if (!isNullOrUndefined(deviceSearchRequestBodyApply) && !listingPageReloadSubjectParameter.isClearFilter) {
          deviceSearchRequest = deviceSearchRequestBodyApply;
        }
        let deviceFilterAction = new DeviceFilterAction(listingPageReloadSubjectParameter, deviceSearchRequest);
        this.callDeviceListFilterRequestParameterSubject(deviceFilterAction);
      } else {
        this.deviceListRefreshSubject.next(
          listingPageReloadSubjectParameter
        );
      }
    }
  }

  /**
  * Cache management methods
  */

  /**
  * Set Package Version List
  * <AUTHOR>
  * @param packageVersionList - Array of package versions to cache
  */
  public setPackageVersionList(packageVersionList: string[]): void {
    this.packageVersionList = packageVersionList;
  }

  /**
  * Get Package Version List
  * <AUTHOR>
  * @returns Cached array of package versions
  */
  public getPackageVersionList(): string[] {
    return this.packageVersionList;
  }

  /**
  * Set Sales Order Number List
  * <AUTHOR>
  * @param salesOrderNumberList - Array of sales order numbers to cache
  */
  public setSalesOrderNumberList(salesOrderNumberList: string[]): void {
    this.salesOrderNumberList = salesOrderNumberList;
  }

  /**
  * Get Sales Order Number List
  * <AUTHOR>
  * @returns Cached array of sales order numbers
  */
  public getSalesOrderNumberList(): string[] {
    return this.salesOrderNumberList;
  }

  /**
  * Set Country List
  * <AUTHOR>
  * @param countryList - Array of countries to cache
  */
  public setCountryList(countryList: CountryListResponse[]): void {
    this.countryList = countryList;
  }

  /**
  * Get Country List
  * <AUTHOR>
  * @returns Cached array of countries
  */
  public getCountryList(): CountryListResponse[] {
    return this.countryList;
  }

  /**
  * Call Device List Refresh Subject
  * Used by device listing component to emit refresh events
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter - The refresh parameters
  */
  public callDeviceListRefreshSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    this.deviceListRefreshSubject.next(listingPageReloadSubjectParameter);
  }

  /**
  * Update cache in background by making fresh API calls
  * This method is called when filter is hidden to keep cache fresh
  * <AUTHOR>
  */
  public async updateCacheInBackground(): Promise<void> {
    // Make API calls in parallel and update cache
    const [packageVersions, salesOrderNumbers, countries] = await Promise.all([
      this.getPackageVersionsFromAPI(),
      this.getSalesOrderNumbersFromAPI(),
      this.getCountriesFromAPI()
    ]);

    // Update cache with fresh data
    this.packageVersionList = packageVersions;
    this.salesOrderNumberList = salesOrderNumbers;
    this.countryList = countries;
  }

  /**
  * Get package versions from API
  * <AUTHOR>
  */
  private async getPackageVersionsFromAPI(): Promise<string[]> {
    const response = await firstValueFrom(this.deviceService.getpackageVersion());
    return this.commonsService.checkForNull(response?.body) || [];
  }

  /**
  * Get sales order numbers from API
  * <AUTHOR>
  */
  private async getSalesOrderNumbersFromAPI(): Promise<string[]> {
    return await this.salesOrderApiCallService.getSalesOrderNumberList();
  }

  /**
  * Get countries from API
  * <AUTHOR>
  */
  private async getCountriesFromAPI(): Promise<CountryListResponse[]> {
    return await this.countryCacheService.getCountryListFromCache(true);
  }

  /**
  * Update only sales order cache when new sales order is added
  * This ensures sales order dropdown has the latest data without calling other APIs
  * <AUTHOR>
  */
  public async updateSalesOrderCacheOnly(): Promise<void> {
    const salesOrderNumbers = await this.getSalesOrderNumbersFromAPI();
    this.salesOrderNumberList = salesOrderNumbers;
  }

  // ==================== DEVICE LIST OPERATIONS ====================

  /**
  * Load device list with search parameters and pagination
  * Handles API call, response processing, and error handling
  * <AUTHOR>
  * @param deviceSearchRequest - Search criteria for filtering devices
  * @param pageObj - Pagination parameters (page, size)
  * @returns Promise with device list result
  */
  public async loadDeviceList(deviceSearchRequest: DeviceSearchRequest, pageObj: any): Promise<DeviceListResult> {
    try {
      const response = await firstValueFrom(this.deviceService.getDeviceList(deviceSearchRequest, pageObj));

      if (response.status === 200) {
        const result = this.processDeviceListResponse(response.body);
        return {
          success: true,
          devices: result.devices,
          totalDeviceDisplay: result.totalDeviceDisplay,
          totalDevice: result.totalDevice,
          localDeviceList: result.localDeviceList,
          totalItems: result.totalItems,
        };
      } else {
        return {
          success: false,
          devices: [],
          totalDeviceDisplay: 0,
          totalDevice: 0,
          localDeviceList: [],
          totalItems: 0,
        };
      }
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      throw error;
    }
  }

  /**
  * Process device list response and extract pagination data
  * <AUTHOR>
  * @param responseBody - API response body
  * @returns Processed device data with pagination info
  */
  private processDeviceListResponse(responseBody: any): DeviceListProcessedResult {
    const devices = responseBody?.content || [];
    const totalDeviceDisplay = responseBody?.numberOfElements || 0;
    const totalDevice = responseBody?.totalElements || 0;
    const localDeviceList = this.extractLocalDeviceIds(devices);
    const totalItems = parseInt(responseBody.totalElements, 10);

    return { devices, totalDeviceDisplay, totalDevice, localDeviceList, totalItems };
  }

  /**
  * Extract local device IDs for checkbox management
  * <AUTHOR>
  * @param deviceContent - Device list from API response
  * @returns Array of local device objects with id and other properties
  */
  private extractLocalDeviceIds(deviceContent: any[]): any[] {
    return deviceContent.map(device => ({
      id: device.id,
      deviceId: device.deviceId,
      editable: device.editable,
      country: device.country,
      deviceType: device.deviceType,
      locked: device.locked,
      productStatus: device.productStatus
    }));
  }

  /**
  * Export device data to CSV format
  * Handles both generation and download of CSV file
  * <AUTHOR>
  * @param deviceIds - Selected device IDs for export
  * @param searchRequest - Current search criteria
  * @returns Promise indicating export completion
  */
  public async exportDeviceCSV(deviceIds: number[]): Promise<void> {
    try {
      // Create empty search request for export - filter will be handled by filter component
      const emptySearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
      const deviceExportCSVSearchRequest = new DeviceExportCSVSearchRequest(deviceIds, new Date().getTimezoneOffset(), emptySearchRequest);

      // Generate CSV file
      const generateResponse = await firstValueFrom(this.deviceService.generateCSVFileForDevice(deviceExportCSVSearchRequest));

      const fileName = generateResponse.body.fileName;

      // Download the generated file
      const downloadResponse = await firstValueFrom(this.deviceService.downloadCSVFileForDevice(fileName));

      this.downloadService.downloadExportCSV("List_of_Device(s).xls", downloadResponse);

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      throw error;
    }
  }

  // ==================== DEVICE TYPE CONVERSION OPERATIONS ====================

  /**
  * Convert devices to Test type
  * Validates device selection and performs type conversion
  * <AUTHOR>
  * @param deviceIds - Array of device IDs to convert
  * @param selectedDevices - Array of selected device objects for validation
  * @returns Promise indicating conversion completion
  */
  public async convertDevicesToTest(deviceIds: number[], selectedDevices: any[]): Promise<void> {
    try {
      if (!this.validateDeviceTypeConversion('TEST_DEVICE', selectedDevices)) {
        this.toastrService.info(DEVICE_ALREADY_TEST);
        return;
      }

      await firstValueFrom(this.deviceService.updateDeviceTypeToTest(deviceIds));

      this.handleDeviceTypeConversionSuccess(DEVICE_CONVERT_TO_TEST);

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      throw error;
    }
  }

  /**
  * Convert devices to Client type
  * Validates device selection and performs type conversion
  * <AUTHOR>
  * @param deviceIds - Array of device IDs to convert
  * @param selectedDevices - Array of selected device objects for validation
  * @returns Promise indicating conversion completion
  */
  public async convertDevicesToClient(deviceIds: number[], selectedDevices: any[]): Promise<void> {
    try {
      if (!this.validateDeviceTypeConversion('CLIENT_DEVICE', selectedDevices)) {
        this.toastrService.info(DEVICE_ALREADY_CLIENT);
        return;
      }

      await firstValueFrom(this.deviceService.updateDeviceTypeToClient(deviceIds));

      this.handleDeviceTypeConversionSuccess(DEVICE_CONVERT_TO_CLIENT);

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      throw error;
    }
  }

  /**
  * Convert devices to Demo type
  * Validates device selection and performs type conversion
  * <AUTHOR>
  * @param deviceIds - Array of device IDs to convert
  * @param selectedDevices - Array of selected device objects for validation
  * @returns Promise indicating conversion completion
  */
  public async convertDevicesToDemo(deviceIds: number[], selectedDevices: any[]): Promise<void> {
    try {
      if (!this.validateDeviceTypeConversion('DEMO_DEVICE', selectedDevices)) {
        this.toastrService.info(DEVICE_ALREADY_DEMO);
        return;
      }

      await firstValueFrom(this.deviceService.updateDeviceTypeToDemo(deviceIds));

      this.handleDeviceTypeConversionSuccess(DEVICE_CONVERT_TO_DEMO);

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      throw error;
    }
  }

  /**
  * Validate if device type conversion is needed
  * Checks if all selected devices are already of the target type
  * <AUTHOR>
  * @param targetDeviceType - Target device type to convert to
  * @param selectedDevices - Array of selected device objects
  * @returns true if conversion is needed, false if all devices are already target type
  */
  private validateDeviceTypeConversion(targetDeviceType: string, selectedDevices: any[]): boolean {
    const selectedDeviceListFilterByType = selectedDevices.filter(device => device.deviceType === targetDeviceType);
    return selectedDeviceListFilterByType.length !== selectedDevices.length;
  }

  /**
  * Handle successful device type conversion
  * Shows success message and triggers page refresh
  * <AUTHOR>
  * @param message - Success message to display
  */
  private handleDeviceTypeConversionSuccess(message: string): void {
    this.toastrService.success(message);
    // Trigger page refresh through existing subject
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    this.deviceListRefreshSubject.next(listingPageReloadSubjectParameter);
  }

  // ==================== DEVICE STATE MANAGEMENT OPERATIONS ====================

  /**
  * Lock or unlock devices
  * Handles API call for device state change
  * <AUTHOR>
  * @param deviceIds - Array of device IDs to lock/unlock
  * @param lockState - true to lock, false to unlock
  * @returns Promise indicating operation completion
  */
  public async lockUnlockDevices(deviceIds: number[], lockState: boolean): Promise<void> {
    try {
      const response = await firstValueFrom(
        this.deviceService.updateDeviceState(deviceIds, lockState)
      );

      if (response.status === 200) {
        this.toastrService.success(response.body.message);
        // Trigger page refresh
        const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
        this.deviceListRefreshSubject.next(listingPageReloadSubjectParameter);
      }

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      throw error;
    }
  }

  /**
  * Enable or disable device editing capabilities
  * Uses DeviceActionService for the operation
  * <AUTHOR>
  * @param deviceIds - Array of device IDs to enable/disable
  * @param enableState - true to enable, false to disable
  * @returns Promise indicating operation completion
  */
  public async enableDisableDevices(deviceIds: number[], enableState: boolean): Promise<void> {
    try {
      // This will use the existing DeviceActionService pattern
      const response = await firstValueFrom(this.deviceService.editEnableDisableForDevice(deviceIds, enableState));

      this.toastrService.success(response.body.message);
      // Trigger page refresh
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      this.deviceListRefreshSubject.next(listingPageReloadSubjectParameter);

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      throw error;
    }
  }

  /**
  * Disable product status for devices
  * Shows confirmation dialog and performs disable operation
  * <AUTHOR>
  * @param deviceIds - Array of device IDs to disable
  * @returns Promise indicating operation completion
  */
  public async disableProductStatusForDevices(deviceIds: number[]): Promise<void> {
    try {
      const response = await firstValueFrom(this.deviceService.disableProductStatusForDevice(deviceIds));

      this.toastrService.success(response.body.message);
      // Trigger page refresh
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      this.deviceListRefreshSubject.next(listingPageReloadSubjectParameter);

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      throw error;
    }
  }

  /**
  * Set RMA product status for devices
  * Shows confirmation dialog and performs RMA operation
  * <AUTHOR>
  * @param deviceIds - Array of device IDs to set to RMA
  * @returns Promise indicating operation completion
  */
  public async rmaProductStatusForDevices(deviceIds: number[]): Promise<void> {
    try {
      const response = await firstValueFrom(this.deviceService.rmaProductStatusForDevice(deviceIds));

      this.toastrService.success(response.body.message);
      // Trigger page refresh
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      this.deviceListRefreshSubject.next(listingPageReloadSubjectParameter);

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      throw error;
    }
  }

  // ==================== DEVICE ASSOCIATION OPERATIONS ====================

  /**
  * Associate devices with sales order
  * Handles sales order association and cache updates
  * <AUTHOR>
  * @param deviceIds - Array of device IDs to associate
  * @param salesOrderData - Sales order data for association
  * @param isSalesOrderNewAdd - Flag indicating if this is a new sales order
  * @returns Promise indicating association completion
  */
  public async associateDevicesWithSalesOrder(deviceIds: number[], salesOrderData: any, isSalesOrderNewAdd: boolean): Promise<void> {
    try {
      const response = await firstValueFrom(this.deviceService.associationDeviceWithSalesOrder(deviceIds, salesOrderData));

      // Update cache if new sales order was added
      if (isSalesOrderNewAdd) {
        await this.updateSalesOrderCacheOnly();
      }

      this.toastrService.success(response.body.message);

      // Trigger page refresh
      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      this.deviceListRefreshSubject.next(listingPageReloadSubjectParameter);

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      throw error;
    }
  }

  // ==================== DEVICE SELECTION AND VALIDATION LOGIC ====================

  /**
  * Validate device selection for operations
  * Checks if devices are selected and validates permissions
  * <AUTHOR>
  * @param deviceIds - Array of selected device IDs
  * @param selectedDevices - Array of selected device objects
  * @param resourceName - Resource name for validation (DeviceListResource or DeviceDetailResource)
  * @returns true if validation passes, false otherwise
  */
  public validateDeviceSelection(deviceIds: number[], selectedDevices: any[], resourceName: string): boolean {
    if (deviceIds.length === 0) {
      this.toastrService.info(Device_Select_Message);
      return false;
    }

    return this.validateUserPermissionsAndCountry(selectedDevices, resourceName);
  }

  /**
  * Validate user permissions and country access for selected devices
  * Combines edit state validation and country validation
  * <AUTHOR>
  * @param selectedDevices - Array of selected device objects
  * @param resourceName - Resource name for validation
  * @returns true if validation passes, false otherwise
  */
  public validateUserPermissionsAndCountry(selectedDevices: any[], resourceName: string): boolean {
    const moduleEditState: boolean[] = selectedDevices.map(device => device.editable);

    if (!this.moduleValidationService.validateWithEditableWithMultipalRecoard(moduleEditState, resourceName)) {
      return false;
    }

    return this.validateUserCountryAccess(selectedDevices, resourceName);
  }

  /**
  * Validate user country access for selected devices
  * <AUTHOR>
  * @param selectedDevices - Array of selected device objects
  * @param resourceName - Resource name for validation
  * @returns true if validation passes, false otherwise
  */
  public validateUserCountryAccess(selectedDevices: any[], resourceName: string): boolean {
    if (selectedDevices.length === 0) {
      this.toastrService.info(Device_Select_Message);
      return false;
    }
    const moduleCountry: string[] = this.getDeviceAssociatedCountries(selectedDevices);
    return this.moduleValidationService.validateWithUserCountryForMultileRecord(moduleCountry, resourceName, true);
  }

  /**
  * Get countries associated with selected devices
  * <AUTHOR>
  * @param selectedDevices - Array of selected device objects
  * @returns Array of country names
  */
  public getDeviceAssociatedCountries(selectedDevices: any[]): string[] {
    return selectedDevices.map(device => device.country);
  }

  /**
  * Validate single device permissions and country access
  * Used for device detail operations
  * <AUTHOR>
  * @param device - Single device object
  * @param resourceName - Resource name for validation (DeviceDetailResource)
  * @returns true if validation passes, false otherwise
  */
  public validateSingleDevicePermissions(device: any, resourceName: string): boolean {
    if (!this.moduleValidationService.validateWithEditStateForSingleRecord(device?.editable, resourceName)) {
      return false;
    }

    return this.moduleValidationService.validateWithUserCountryForSingleRecord(device?.country, resourceName, true);
  }

  /**
  * Validate user country access for single device
  * <AUTHOR>
  * @param device - Single device object
  * @param resourceName - Resource name for validation
  * @returns true if validation passes, false otherwise
  */
  public validateSingleDeviceCountryAccess(device: any, resourceName: string): boolean {
    return this.moduleValidationService.validateWithUserCountryForSingleRecord(device.country, resourceName, true);
  }

  // ==================== DEVICE DETAIL OPERATIONS ====================

  /**
  * Load device detail information
  * Handles API call and response processing for device detail
  * <AUTHOR>
  * @param deviceId - Device ID to load details for
  * @returns Promise with device detail result
  */
  public async loadDeviceDetail(deviceId: number): Promise<DeviceDetailResult> {
    try {
      const response = await firstValueFrom(
        this.deviceService.getDeviceDetail(deviceId)
      );

      if (response.status === 200) {
        const deviceDetail = response.body;
        const releaseVersionId = (deviceDetail?.releaseId == null) ? -1 : deviceDetail.releaseId;

        return {
          success: true,
          deviceDetail,
          releaseVersionId,
          transferProductDetails: new TransferProductDetails(
            deviceId,
            deviceDetail.salesOrderId,
            deviceDetail.deviceSerialNo,
            null,
            DeviceDetailResource
          )
        };
      } else {
        return {
          success: false,
          deviceDetail: null,
          releaseVersionId: -1,
          transferProductDetails: null
        };
      }
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      throw error;
    }
  }

  /**
  * Get release versions for device
  * Loads available release versions based on device type, country, and package version
  * <AUTHOR>
  * @param deviceType - Device type enum
  * @param countryId - Country ID
  * @param packageVersion - Package version string
  * @param hasUpdatePermission - Whether user has update permission
  * @returns Promise with release versions result
  */
  public async getReleaseVersions(
    deviceType: deviceTypesEnum,
    countryId: number,
    packageVersion: string,
    hasUpdatePermission: boolean
  ): Promise<ReleaseVersionsResult> {
    try {
      if (deviceType === deviceTypesEnum.TEST_DEVICE && hasUpdatePermission) {
        if (!isNullOrUndefined(countryId)) {
          const requestObject = new ReleaseVersionRequest(countryId, packageVersion);
          const response = await firstValueFrom(
            this.deviceService.getReleaseVersionDetail(requestObject)
          );

          return {
            success: true,
            releaseVersions: response.body || [],
            selectedReleaseVersion: -1,
            btnReleaseVersionDisable: true
          };
        } else {
          return this.getEmptyReleaseVersionsResult();
        }
      } else {
        return this.getEmptyReleaseVersionsResult();
      }
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      return this.getEmptyReleaseVersionsResult();
    }
  }

  /**
  * Get empty release versions result
  * <AUTHOR>
  * @returns Empty release versions result
  */
  private getEmptyReleaseVersionsResult(): ReleaseVersionsResult {
    return {
      success: true,
      releaseVersions: [],
      selectedReleaseVersion: -1,
      btnReleaseVersionDisable: true
    };
  }

  /**
  * Assign release version to device
  * Handles release version assignment API call
  * <AUTHOR>
  * @param deviceId - Device ID
  * @param releaseVersionId - Release version ID to assign
  * @returns Promise indicating assignment completion
  */
  public async assignReleaseVersion(deviceId: number, releaseVersionId: number): Promise<void> {
    try {
      const requestObject = new AssignSelectedReleaseVersionRequest(deviceId, releaseVersionId);
      const response = await firstValueFrom(
        this.deviceService.assignSelectedReleaseVersion(requestObject)
      );

      if (response.status === 200) {
        this.toastrService.success("Release version assigned successfully");
      } else {
        this.toastrService.error("Error in assigning Release version");
      }

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
    }
  }

  /**
  * Toggle assign button state based on selection
  * <AUTHOR>
  * @param selectedReleaseVersion - Currently selected release version
  * @param currentReleaseVersionId - Current device release version ID
  * @returns true if button should be disabled, false otherwise
  */
  public shouldDisableAssignButton(selectedReleaseVersion: number, currentReleaseVersionId: number): boolean {
    return selectedReleaseVersion === -1 || selectedReleaseVersion === currentReleaseVersionId;
  }

  // ==================== FILTER INTEGRATION LOGIC ====================

  /**
  * Validate filter form data
  * Checks if at least one filter field has a value
  * <AUTHOR>
  * @param formValue - Filter form values
  * @returns true if form has valid filter data, false otherwise
  */
  public validateFilterForm(formValue: any): boolean {
    return !(
      this.commonsService.checkValueIsNullOrEmpty(formValue?.deviceId) &&
      this.commonsService.checkValueIsNullOrEmpty(formValue?.deviceSerialNo) &&
      this.commonsService.checkValueIsNullOrEmpty(formValue?.customerName) &&
      this.commonsService.checkValueIsNullOrEmpty(formValue?.packageVersions) &&
      this.commonsService.checkValueIsNullOrEmpty(formValue?.connectionState) &&
      this.commonsService.checkValueIsNullOrEmpty(formValue?.deviceLockState) &&
      this.commonsService.checkValueIsNullOrEmpty(formValue?.deviceEditState) &&
      this.commonsService.checkValueIsNullOrEmpty(formValue?.countries) &&
      this.commonsService.checkValueIsNullOrEmpty(formValue?.drpDeviceType) &&
      this.commonsService.checkValueIsNullOrEmpty(formValue?.salesOrderNumber) &&
      this.commonsService.checkValueIsNullOrEmpty(formValue?.productStatus)
    );
  }

  /**
  * Build device search request from filter form data
  * Processes form values and creates DeviceSearchRequest object
  * <AUTHOR>
  * @param formValue - Filter form values
  * @returns DeviceSearchRequest object
  */
  public buildDeviceSearchRequest(formValue: any): DeviceSearchRequest {
    // Process text fields
    const deviceId = this.commonsService.checkNullFieldValue(formValue.deviceId);
    const customerName = this.commonsService.checkNullFieldValue(formValue.customerName);
    const deviceSerialNo = this.commonsService.checkNullFieldValue(formValue.deviceSerialNo);

    // Process country selection
    const countryValue = formValue.countries;
    const countryIds: number[] = isNullOrUndefined(countryValue) ? null : this.commonsService.getIdsFromArray(countryValue);

    // Process enum selections
    const productStatus = this.commonsService.getSelectedValueFromEnum(formValue.productStatus);
    const connectionState = this.commonsService.getSelectedValueFromEnum(formValue.connectionState);
    const locked = this.commonsService.getSelectedValueFromBooleanKeyValueMapping(formValue.deviceLockState);
    const deviceEditState = this.commonsService.getSelectedValueFromBooleanKeyValueMapping(formValue.deviceEditState);

    return new DeviceSearchRequest(
      this.commonsService.checkNullFieldValue(formValue.packageVersions),
      (connectionState.length == 1) ? connectionState[0] : null,
      this.commonsService.getDeviceTypeStringToEnum(formValue.drpDeviceType),
      deviceId,
      deviceSerialNo,
      customerName,
      countryIds,
      locked,
      deviceEditState,
      this.commonsService.checkNullFieldValue(formValue.salesOrderNumber),
      productStatus
    );
  }

  /**
  * Process filter search request
  * Validates form and triggers device list refresh
  * <AUTHOR>
  * @param formValue - Filter form values
  * @param isFormInvalid - Whether the form is invalid
  * @param listingPageReloadSubjectParameter - Page reload parameters
  * @returns true if search was processed, false if validation failed
  */
  public processFilterSearch(
    formValue: any,
    isFormInvalid: boolean,
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter
  ): boolean {
    if (isFormInvalid || !this.validateFilterForm(formValue)) {
      this.toastrService.info('Please provide at least one filter criteria');
      return false;
    }

    const deviceSearchRequest = this.buildDeviceSearchRequest(formValue);
    const deviceFilterAction = new DeviceFilterAction(listingPageReloadSubjectParameter, deviceSearchRequest);
    this.callDeviceListFilterRequestParameterSubject(deviceFilterAction);
    return true;
  }

  /**
  * Clear all filter data and refresh device list
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter - Page reload parameters
  */
  public clearAllFiltersAndRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    // Create empty search request
    const emptySearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const deviceFilterAction = new DeviceFilterAction(listingPageReloadSubjectParameter, emptySearchRequest);
    this.callDeviceListFilterRequestParameterSubject(deviceFilterAction);
  }

}
