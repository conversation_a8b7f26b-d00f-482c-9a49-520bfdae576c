import { Pipe, PipeTransform } from '@angular/core';
import { ClientDevice, DemoDevice, TestDevice } from 'src/app/app.constants';
import { isNullOrUndefined } from 'is-what';
import { deviceTypesEnum } from '../enum/deviceTypesEnum.enum';

@Pipe({
  name: 'deviceTypeName'
})
export class DeviceTypeNamePipe implements PipeTransform {

  transform(deviceType: deviceTypesEnum): any {
    if (isNullOrUndefined(deviceType)) {
      return null;
    } else {
      if (deviceType == deviceTypesEnum.DEMO_DEVICE) {
        return DemoDevice;
      } else if (deviceType == deviceTypesEnum.CLIENT_DEVICE) {
        return ClientDevice;
      } else if (deviceType == deviceTypesEnum.TEST_DEVICE) {
        return TestDevice;
      } else {
        return null;
      }
    }
  }

}
