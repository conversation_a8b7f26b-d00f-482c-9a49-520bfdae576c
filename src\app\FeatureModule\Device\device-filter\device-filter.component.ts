import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { DeviceFilterAction } from 'src/app/model/device/DeviceFilterAction.model';
import { CUSTOMER_NAME, ClientDevice, DEVICE_CONNECTION_STATE, DEVICE_COUNTRY, DEVICE_CUSTOMER_NAME, DEVICE_EDITABLE, DEVICE_HW_ID, DEVICE_LOCKED, DEVICE_SALES_ORDER_NUMBER, DEVICE_SERIAL_NO, DEVICE_STATUS, DEVICE_SYSTEM_SW_VERSION, DEVICE_TYPE, DemoDevice, EDITABLE, FILTER, FILTER_CLEAR_BUTTON, FILTER_SEARCH_BUTTON, LOCKED, MAXIMUM_TEXTBOX_LENGTH, MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, SPECIAL_CHARACTER_ERROR_MESSAGE, SPECIAL_CHARACTER_PATTERN, STATUS, TestDevice } from '../../../app.constants';
import { CountryListResponse } from '../../../model/Country/CountryListResponse.model';
import { MultiSelectDropdownSettings } from '../../../model/MultiSelectDropdownSettings.model';
import { BooleanKeyValueMapping } from '../../../model/common/BooleanKeyValueMapping.model';
import { EnumMapping } from '../../../model/common/EnumMapping.model';
import { ListingPageReloadSubjectParameter } from '../../../model/common/listingPageReloadSubjectParameter.model';
import { DeviceSearchRequest } from '../../../model/device/deviceSearchRequest.model';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { SalesOrderApiCallService } from '../../../shared/Service/SalesOrderService/sales-order-api-call.service';
import { DeviceService } from '../../../shared/device.service';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { DeviceConnectionState } from '../../../shared/enum/Device/DeviceConnectionState.model';
import { CommonsService } from '../../../shared/util/commons.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { MultiSelectDropDownSettingService } from '../../../shared/util/multi-select-drop-down-setting.service';
import { DeviceOperationService } from '../DeviceService/Device-Operation/device-operation.service';

@Component({
  selector: 'app-device-filter',
  templateUrl: './device-filter.component.html',
  styleUrls: ['./device-filter.component.css']
})
export class DeviceFilterComponent implements OnInit, OnDestroy {

  @Input("isFilterComponentInitWithApicall") isFilterComponentInitWithApicall: boolean;
  @Input("deviceSearchRequestBody") deviceSearchRequestBody: DeviceSearchRequest;
  @Input("listPageRefreshForbackToDetailPage") listPageRefreshForbackToDetailPage: boolean;

  // Filter form
  filterForm: FormGroup;

  // Dropdown data
  countryList: CountryListResponse[] = [];
  salesOrderNumberList: string[] = [];
  deviceTypes: string[];
  lockUnlockStateList: Array<BooleanKeyValueMapping> = [];
  editStateList: Array<BooleanKeyValueMapping> = [];
  productStatusList: Array<EnumMapping> = [];
  deviceConnectionState: Array<EnumMapping> = [];
  packageVersionsList: Array<string>;

  // Dropdown settings
  dropdownSettingsForLockState: MultiSelectDropdownSettings;
  dropdownSettingsForEditState: MultiSelectDropdownSettings;
  systemSoftwearVersionDropdownSetting: MultiSelectDropdownSettings;
  deviceConnectionStateDropdownSetting: MultiSelectDropdownSettings;
  dropdownSettingsDeviceType: MultiSelectDropdownSettings;
  salesOrderNumberSetting: MultiSelectDropdownSettings;
  countrySetting: MultiSelectDropdownSettings;
  dropdownSettingsForProductStatus: MultiSelectDropdownSettings;

  //Filter Constants
  filter: string = FILTER;
  searchBtnText: string = FILTER_SEARCH_BUTTON;
  clearBtnText: string = FILTER_CLEAR_BUTTON;
  serialNo: string = DEVICE_SERIAL_NO;
  hwId: string = DEVICE_HW_ID;
  salesOrderNumber: string = DEVICE_SALES_ORDER_NUMBER;
  deviceType: string = DEVICE_TYPE;
  country: string = DEVICE_COUNTRY;
  systemSwVerstion: string = DEVICE_SYSTEM_SW_VERSION;
  connectionState: string = DEVICE_CONNECTION_STATE;
  costmoerName: string = DEVICE_CUSTOMER_NAME;
  locked: string = DEVICE_LOCKED;
  editable: string = DEVICE_EDITABLE;
  status: string = DEVICE_STATUS;


  // Text box validation messages
  textBoxMaxCharactersAllowedMessage: string = MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  small_textBoxMaxCharactersAllowedMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;

  // Subscriptions
  subscriptionForRefreshList: Subscription;

  defaultListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

  constructor(
    private fb: FormBuilder,
    private commonsService: CommonsService,
    private deviceOperationService: DeviceOperationService,
    private deviceService: DeviceService,
    private salesOrderApiCallService: SalesOrderApiCallService,
    private countryCacheService: CountryCacheService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private keyValueMappingServiceService: KeyValueMappingServiceService,
    private exceptionService: ExceptionHandlingService
  ) {
    this.initializeFilterForm();
  }

  /**
  * Initialize the component
  *
  * <AUTHOR>
  */
  public ngOnInit(): void {
    this.initializeDropdownSettings();
    this.initializeDropdownData();
    this.getInitCall();
    this.onInitSubject();
    if (this.isFilterComponentInitWithApicall) {
      this.clearFilter(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
  * Destroy the component
  *
  * <AUTHOR>
  */
  public ngOnDestroy(): void {
    if (this.subscriptionForRefreshList) {
      this.subscriptionForRefreshList.unsubscribe();
    }
  }

  /**
  * Initialize the filter form with all form controls
  * <AUTHOR>
  */
  private initializeFilterForm(): void {
    this.filterForm = this.fb.group({
      packageVersions: new FormControl([], []),
      connectionState: [],
      deviceLockState: [],
      deviceEditState: [],
      countries: [],
      drpDeviceType: [],
      salesOrderNumber: [],
      productStatus: new FormControl([], []),
      deviceId: new FormControl('', [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
      deviceSerialNo: new FormControl('', [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
      customerName: new FormControl('', [Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)])
    });
  }

  /**
  * Initialize dropdown settings
  * <AUTHOR>
  */
  private initializeDropdownSettings(): void {
    this.dropdownSettingsForLockState = this.multiSelectDropDownSettingService.getLockStateDropdownSetting();
    this.dropdownSettingsForEditState = this.multiSelectDropDownSettingService.getEditStateDropdownSetting();
    this.systemSoftwearVersionDropdownSetting = this.multiSelectDropDownSettingService.getSystemSoftwearVersionDropdownSetting();
    this.deviceConnectionStateDropdownSetting = this.multiSelectDropDownSettingService.getDeviceConnectionStateDropdownSetting();
    this.dropdownSettingsDeviceType = this.multiSelectDropDownSettingService.getDeviceTypeDropdownSetting();
    this.salesOrderNumberSetting = this.multiSelectDropDownSettingService.getSalesOrderNumberDrpSetting(false, 'Select All', 'UnSelect All', false);
    this.countrySetting = this.multiSelectDropDownSettingService.getCountryDrpSetting(false, false);
    this.dropdownSettingsForProductStatus = this.multiSelectDropDownSettingService.getProductStatusDrpSetting();
  }

  /**
  * Initialize dropdown data
  * <AUTHOR>
  */
  private initializeDropdownData(): void {
    this.productStatusList = this.keyValueMappingServiceService.enumOptionToList(ProductStatusEnum);
    this.deviceConnectionState = this.keyValueMappingServiceService.enumOptionToList(DeviceConnectionState);
    this.deviceTypes = [ClientDevice, TestDevice, DemoDevice];
    this.lockUnlockStateList = this.keyValueMappingServiceService.lockedUnlockOptionList();
    this.editStateList = this.keyValueMappingServiceService.editEnableDisableOptionList();
  }

  /**
  * Initialize subject subscriptions
  * <AUTHOR>
  */
  public onInitSubject(): void {
    this.subscriptionForRefreshList = this.deviceOperationService
      ?.getDeviceListRefreshSubject()
      ?.subscribe(
        (listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
          if (listingPageReloadSubjectParameter.isReloadData) {
            if (listingPageReloadSubjectParameter.isClearFilter) {
              this.clearFilter(listingPageReloadSubjectParameter);
            } else {
              //page change 1,2,3
              this.deviceListPageRefresh(listingPageReloadSubjectParameter)
            }
          }
        }
      );
  }

  /**
  * Get initial data for dropdowns
  * <AUTHOR>
  */
  public async getInitCall(): Promise<void> {
    if (this.deviceOperationService.getPackageVersionList().length === 0) {
      await this.getpackageVersion();
      this.deviceOperationService.setPackageVersionList(this.packageVersionsList);
    } else {
      this.packageVersionsList = this.deviceOperationService.getPackageVersionList();
    }

    if (this.deviceOperationService.getSalesOrderNumberList().length === 0) {
      await this.updateSalesOrderList();
      this.deviceOperationService.setSalesOrderNumberList(this.salesOrderNumberList);
    } else {
      this.salesOrderNumberList = this.deviceOperationService.getSalesOrderNumberList();
    }

    if (this.deviceOperationService.getCountryList().length === 0) {
      await this.getCountryList();
      this.deviceOperationService.setCountryList(this.countryList);
    } else {
      this.countryList = this.deviceOperationService.getCountryList();
    }

    this.setFilterValue();
  }

  /**
  * Set Filter value
  *
  * Note : if user hide and show filter then set data in storage
  *
  * <AUTHOR>
  */
  private setFilterValue(): void {
    if (this.deviceSearchRequestBody != null) {
      let connectionState = this.deviceSearchRequestBody.status ? this.commonsService.getEnumMappingSelectedValue(DeviceConnectionState, [this.deviceSearchRequestBody.status]) : [];
      let deviceLockState = this.deviceSearchRequestBody.deviceLockStatus !== null ? this.lockUnlockStateList.filter(state => state.value === this.deviceSearchRequestBody.deviceLockStatus) : [];
      let deviceEditState = this.deviceSearchRequestBody.isEditable !== null ? this.editStateList.filter(state => state.value === this.deviceSearchRequestBody.isEditable) : [];
      let countries = this.deviceSearchRequestBody.countryIds ? this.countryList.filter(country => this.deviceSearchRequestBody.countryIds.includes(country.id)) : [];
      let productStatus = this.deviceSearchRequestBody.productStatus ? this.commonsService.getEnumMappingSelectedValue(ProductStatusEnum, this.deviceSearchRequestBody.productStatus) : [];

      this.filterForm.get('packageVersions').setValue(this.deviceSearchRequestBody.packageVersions || []);
      this.filterForm.get('connectionState').setValue(connectionState);
      this.filterForm.get('deviceLockState').setValue(deviceLockState);
      this.filterForm.get('deviceEditState').setValue(deviceEditState);
      this.filterForm.get('countries').setValue(countries);
      this.filterForm.get('drpDeviceType').setValue([this.deviceSearchRequestBody.deviceType]);
      this.filterForm.get('salesOrderNumber').setValue(this.deviceSearchRequestBody.salesOrderNumbers || []);
      this.filterForm.get('productStatus').setValue(productStatus);
      this.filterForm.get('deviceId').setValue(this.deviceSearchRequestBody.deviceId || '');
      this.filterForm.get('deviceSerialNo').setValue(this.deviceSearchRequestBody.deviceSerialNo || '');
      this.filterForm.get('customerName').setValue(this.deviceSearchRequestBody.customerName || '');
    }
    if (this.listPageRefreshForbackToDetailPage) {
      this.deviceListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
  * Get package versions for filter dropdown and cache the result
  * @returns Promise<void>
  * <AUTHOR>
  */
  private async getpackageVersion(): Promise<void> {
    this.deviceService.getpackageVersion()?.subscribe({
      next: (res) => {
        this.packageVersionsList = this.commonsService.checkForNull(res.body);
        // Cache the result in the service
        this.deviceOperationService.setPackageVersionList(this.packageVersionsList);
      },
      error: (error) => {
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
  * Get sales order list for filter dropdown and cache the result
  * @returns Promise<void>
  * <AUTHOR>
  */
  private async updateSalesOrderList(): Promise<void> {
    this.salesOrderNumberList = await this.salesOrderApiCallService.getSalesOrderNumberList();
    // Cache the result in the service
    this.deviceOperationService.setSalesOrderNumberList(this.salesOrderNumberList);
  }

  /**
  * Get country list for filter dropdown and cache the result
  * @returns Promise<void>
  * <AUTHOR>
  */
  private async getCountryList(): Promise<void> {
    this.countryList = await this.countryCacheService.getCountryListFromCache(true);
    // Cache the result in the service
    this.deviceOperationService.setCountryList(this.countryList);
  }

  /**
  * Handle country selection in filter
  * <AUTHOR>
  */
  public onCountrySelect(item: any): void {
    if (item.id == -1) {
      this.filterForm.get('countries').setValue([item]);
      this.countryList = this.multiSelectDropDownSettingService.setOtherOptionDisabled(this.countryList);
    }
  }

  /**
  * Handle country deselection in filter
  * <AUTHOR>
  */
  public onCountryDeSelect(): void {
    this.countryList = this.multiSelectDropDownSettingService.setAllOptionEnable(this.countryList);
  }

  /**
  * Reload Listing Data
  * <AUTHOR>
  */
  public searchData(): void {
    const allFormValue = this.filterForm.value;
    this.filterForm.get('deviceId').setValue(this.commonsService.checkNullFieldValue(allFormValue.deviceId));
    this.filterForm.get('deviceSerialNo').setValue(this.commonsService.checkNullFieldValue(allFormValue.deviceSerialNo));
    this.filterForm.get('customerName').setValue(this.commonsService.checkNullFieldValue(allFormValue.customerName));

    const searchProcessed = this.deviceOperationService.processFilterSearch(allFormValue, this.filterForm.invalid, this.defaultListingPageReloadSubjectParameter);

    if (!searchProcessed) {
      // Validation failed - message already shown by service
      return;
    }
  }

  /**
  * Clear all filters and reload data
  * <AUTHOR>
  */
  public clearFilter(listingPageReloadSubjectParameter?: ListingPageReloadSubjectParameter): void {
    this.clearAllFilter();
    const reloadParam = listingPageReloadSubjectParameter || this.defaultListingPageReloadSubjectParameter;
    this.deviceOperationService.clearAllFiltersAndRefresh(reloadParam);
  }

  /**
  * Clear all filter fields
  * <AUTHOR>
  */
  private clearAllFilter(): void {
    this.onCountryDeSelect();
    this.filterForm.reset();
    this.filterForm.patchValue({
      packageVersions: [],
      connectionState: null,
      deviceLockState: null,
      deviceEditState: null,
      countries: null,
      drpDeviceType: null,
      deviceId: null,
      deviceSerialNo: null,
      customerName: null,
      salesOrderNumber: [],
      productStatus: []
    });
  }

  /**
  * Send filter data to listing component and reload page
  * <AUTHOR>
  */
  private deviceListPageRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    if (this.filterForm.invalid) {
      this.filterForm.reset();
    }

    const deviceSearchRequest = this.deviceOperationService.buildDeviceSearchRequest(this.filterForm.value);
    const deviceFilterAction = new DeviceFilterAction(listingPageReloadSubjectParameter, deviceSearchRequest);
    this.deviceOperationService.callDeviceListFilterRequestParameterSubject(deviceFilterAction);
  }
}
