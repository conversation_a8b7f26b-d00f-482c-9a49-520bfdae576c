import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { Subject } from 'rxjs';
import { ITEMS_PER_PAGE } from '../../../app.constants';
import { ConfirmDialogService } from '../../../confirmationdialog/confirmation.service';
import { NoDataMessageComponent } from '../../NoData/no-data-message/no-data-message.component';
import { ListingPageReloadSubjectParameter } from '../../../model/common/listingPageReloadSubjectParameter.model';
import { Pageable } from '../../../model/common/pageable.model';
import { Sort } from '../../../model/common/sort.model';
import { IDevice } from '../../../model/device.model';
import { DeviceSearchRequest } from '../../../model/device/deviceSearchRequest.model';
import { DeviceListResponse } from '../../../model/deviceListResponst.model';
import { BasicSalesOrderDetailResponse } from '../../../model/SalesOrder/BasicSalesOrderDetailResponse.model';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { DeviceService } from '../../../shared/device.service';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { deviceTypesEnum } from '../../../shared/enum/deviceTypesEnum.enum';
import { collapseFilterTextEnum } from '../../../shared/enum/collapseFilterButtonText.enum';
import { DeviceListOperations } from '../../../shared/enum/Operations/DeviceListOperations.enum';
import { ImportCsvFileApiService } from '../../../shared/importFileService/import-csv-file-api.service';
import { CustomerAssociationService } from '../../../shared/modalservice/customer-association.service';
import { PermissionService } from '../../../shared/permission.service';
import { EnumMappingDisplayNamePipe } from '../../../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { DeviceTypeNamePipe } from '../../../shared/pipes/device-type-name.pipe';
import { PrintListPipe } from '../../../shared/pipes/printList.pipe';
import { HidePermissionNamePipe } from '../../../shared/pipes/Role/hidePermissionName.pipe';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { DeviceOperationService } from '../DeviceService/Device-Operation/device-operation.service';
import { RoleApiCallService } from '../../../shared/Service/RoleService/role-api-call.service';
import { SalesOrderApiCallService } from '../../../shared/Service/SalesOrderService/sales-order-api-call.service';
import { SSOLoginService } from '../../../shared/Service/SSO/ssologin.service';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { commonsProviders } from '../../../Tesing-Helper/test-utils';
import { DeviceFilterComponent } from '../device-filter/device-filter.component';
import { DeviceFilterAction } from '../../../model/device/DeviceFilterAction.model';
import { DeviceComponent } from './device-list.component';


describe('DeviceComponent', () => {
  let component: DeviceComponent;
  let fixture: ComponentFixture<DeviceComponent>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let deviceServiceMock: jasmine.SpyObj<DeviceService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let importCsvfileApiServiceSpy: jasmine.SpyObj<ImportCsvFileApiService>;
  let salesOrderApiCallServiceSpy: jasmine.SpyObj<SalesOrderApiCallService>;
  let deviceOperationServiceSpy: jasmine.SpyObj<DeviceOperationService>;
  let customerAssociationServiceSpy: jasmine.SpyObj<CustomerAssociationService>;

  let deviceList: Array<IDevice> = [
    new IDevice(
      63,
      "maitri-test-function-audit1",
      "1.0.0.0",
      ProductStatusEnum.DISABLED,
      null,
      true,
      deviceTypesEnum.DEMO_DEVICE,
      true,
      null,
      "20250123",
      "mohit1",
      "Japan"
    ),
    new IDevice(
      64,
      "maitri-test-function-audit2",
      "1.0.0.0",
      ProductStatusEnum.DISABLED,
      null,
      true,
      deviceTypesEnum.DEMO_DEVICE,
      false,
      null,
      "20250123",
      "mohit1",
      "Japan"
    ),
    new IDevice(
      65,
      "maitri-test-function-audit3",
      "1.0.0.0",
      ProductStatusEnum.DISABLED,
      null,
      true,
      deviceTypesEnum.TEST_DEVICE,
      true,
      null,
      "20250123",
      "mohit1",
      "Japan"
    )
  ];

  const sort = new Sort(true, false, false);
  const pageable = new Pageable(sort, 0, 10, 0, true, false);
  let deviceListResponse: DeviceListResponse = new DeviceListResponse(pageable, 3, false, 3, 10, true, sort, 10, 0, false, deviceList);

  // Mock BasicSalesOrderDetailResponse
  const mockBasicSalesOrderDetailResponse: BasicSalesOrderDetailResponse = new BasicSalesOrderDetailResponse(
    'SO123',
    'Test Customer',
    '<EMAIL>',
    1,
    'PO123',
    false,
    false,
    'Standard'
  );



  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info', 'clear']);
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getDevicePermission', 'getProbPermission', 'getJobPermission', 'getSoftwearBuildPermission', 'getDeviceLogPermission', 'getUserPermission', 'getVideoPermission', 'getRolePermission', 'getKitManagementPermission', 'getSalesOrderPermission', 'getCountryPermission', 'getAuditPermission', 'getProbeConfigGroupPermission']);
    deviceServiceMock = jasmine.createSpyObj('DeviceService', ['getDeviceList', 'getpackageVersion', 'editEnableDisableForDevice', 'updateDeviceState', 'updateDeviceTypeToTest', 'updateDeviceTypeToClient', 'updateDeviceTypeToDemo', 'rmaProductStatusForDevice', 'disableProductStatusForDevice', 'generateCSVFileForDevice', 'downloadCSVFileForDevice', 'getDeviceDetail', 'getReleaseVersionDetail', 'associationDeviceWithSalesOrder', 'assignSelectedReleaseVersion']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheServiceSpy', ['getCountryListFromCache']);
    importCsvfileApiServiceSpy = jasmine.createSpyObj('ImportCsvfileApiService', ['downloadCsvTemplate', 'importFileForUpdateTemplateData']);
    salesOrderApiCallServiceSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderNumberList', 'getBasicSalesOrderDetails']);
    deviceOperationServiceSpy = jasmine.createSpyObj('DeviceOperationService', [
      'getDeviceListFilterRequestParameterSubject',
      'getDeviceListRefreshSubject',
      'callRefreshPageSubject',
      'loadDeviceList',
      'updateCacheInBackground',
      'updateSalesOrderCacheOnly',
      'validateDeviceSelection',
      'validateUserCountryAccess',
      'convertDevicesToTest',
      'convertDevicesToClient',
      'convertDevicesToDemo',
      'lockUnlockDevices',
      'enableDisableDevices',
      'associateDevicesWithSalesOrder',
      'disableProductStatusForDevices',
      'rmaProductStatusForDevices',
      'exportDeviceCSV',
      'clearAllFiltersAndRefresh'
    ]);

    customerAssociationServiceSpy = jasmine.createSpyObj('CustomerAssociationService', ['openCustomerAssociationPopup']);

    // Setup DeviceOperationService subjects
    const deviceFilterSubject = new Subject<DeviceFilterAction>();
    const deviceRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();
    deviceOperationServiceSpy.getDeviceListFilterRequestParameterSubject.and.returnValue(deviceFilterSubject);
    deviceOperationServiceSpy.getDeviceListRefreshSubject.and.returnValue(deviceRefreshSubject);

    // Mock loadDeviceList to return the device list response
    deviceOperationServiceSpy.loadDeviceList.and.returnValue(Promise.resolve({
      success: true,
      devices: deviceListResponse.content,
      totalDeviceDisplay: deviceListResponse.numberOfElements,
      totalDevice: deviceListResponse.totalElements,
      localDeviceList: deviceListResponse.content,
      totalItems: deviceListResponse.totalElements
    }));

    await TestBed.configureTestingModule({
      declarations: [
        DeviceComponent,
        NoDataMessageComponent,
        DeviceTypeNamePipe,
        EnumMappingDisplayNamePipe,
        DeviceFilterComponent
      ],
      imports: [CommonModule, ReactiveFormsModule, FormsModule, NgbPaginationModule, NgMultiSelectDropDownModule.forRoot()
      ],
      providers: [
        ConfirmDialogService,
        CommonsService,
        EnumMappingDisplayNamePipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: AuthJwtService, useValue: authServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: DeviceService, useValue: deviceServiceMock },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: ImportCsvFileApiService, useValue: importCsvfileApiServiceSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceSpy },
        { provide: DeviceOperationService, useValue: deviceOperationServiceSpy },
        { provide: CustomerAssociationService, useValue: customerAssociationServiceSpy },
        SessionStorageService,
        SSOLoginService,
        RoleApiCallService,
        CommonOperationsService,
        HidePermissionNamePipe,
        PrintListPipe,
        KeyValueMappingServiceService,
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DeviceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  // Test cases start here
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize component when user is not authenticated', () => {
      // Arrange
      authServiceSpy.isAuthenticate.and.returnValue(false);

      // Act
      component.ngOnInit();

      // Assert
      expect(authServiceSpy.loginNavigate).toHaveBeenCalled();
    });

    it('should initialize component when user is authenticated with device permissions', () => {
      // Arrange
      authServiceSpy.isAuthenticate.and.returnValue(true);
      permissionServiceSpy.getDevicePermission.and.returnValue(true);
      spyOn(component, 'getDeviceData');

      // Act
      component.ngOnInit();

      // Assert
      expect(component.deviceRederPermission).toBe(true);
      expect(component.checkboxDisplayPermission).toBe(true);
      expect(component.getDeviceData).toHaveBeenCalled();
    });

    it('should initialize component when user is authenticated without device permissions', () => {
      // Arrange
      authServiceSpy.isAuthenticate.and.returnValue(true);
      permissionServiceSpy.getDevicePermission.and.returnValue(false);
      spyOn(component, 'getDeviceData');

      // Act
      component.ngOnInit();

      // Assert
      expect(component.deviceRederPermission).toBe(false);
      expect(component.checkboxDisplayPermission).toBe(false);
      expect(component.getDeviceData).not.toHaveBeenCalled();
    });
  });

  describe('loadAll', () => {
    it('should load devices successfully', async () => {
      // Arrange
      const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
      component.page = 1;
      component.itemsPerPage = 10;

      // Act
      await component.loadAll(mockSearchRequest);

      // Assert
      expect(deviceOperationServiceSpy.loadDeviceList).toHaveBeenCalledWith(
        mockSearchRequest,
        { page: 0, size: 10 }
      );
      expect(component.devices).toEqual(deviceListResponse.content);
      expect(component.totalDeviceDisplay).toBe(deviceListResponse.numberOfElements);
      expect(component.totalDevice).toBe(deviceListResponse.totalElements);
      expect(component.loading).toBe(false);
    });

    it('should handle load devices failure', async () => {
      // Arrange
      const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
      deviceOperationServiceSpy.loadDeviceList.and.returnValue(Promise.resolve({
        success: false,
        devices: [],
        totalDeviceDisplay: 0,
        totalDevice: 0,
        localDeviceList: [],
        totalItems: 0
      }));

      // Act
      await component.loadAll(mockSearchRequest);

      // Assert
      expect(component.devices).toEqual([]);
      expect(component.totalDeviceDisplay).toBe(0);
      expect(component.totalDevice).toBe(0);
      expect(component.loading).toBe(false);
    });
  });

  describe('loadPage', () => {
    it('should load new page when page is different', () => {
      // Arrange
      component.previousPage = 1;
      spyOn(component, 'clearDeviceIdCheckBox');
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      // Act
      component.loadPage(2);

      // Assert
      expect(component.previousPage).toBe(2);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(false, false);
    });

    it('should not load page when page is same', () => {
      // Arrange
      component.previousPage = 1;
      spyOn(component, 'clearDeviceIdCheckBox');
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      // Act
      component.loadPage(1);

      // Assert
      expect(component.clearDeviceIdCheckBox).not.toHaveBeenCalled();
      expect(component.filterPageSubjectCallForReloadPage).not.toHaveBeenCalled();
    });
  });

  describe('changeDataSize', () => {
    it('should change data size and reload page', () => {
      // Arrange
      const mockEvent = { target: { value: '25' } };
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      // Act
      component.changeDataSize(mockEvent);

      // Assert
      expect(component.itemsPerPage).toBe('25');
      expect(component.deviceIdList).toEqual([]);
      expect(component.selectedDeviceList).toEqual([]);
      expect(component.loading).toBe(true);
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, false);
    });
  });

  describe('changeDeviceOperation', () => {
    let mockSelectElement: HTMLSelectElement;

    beforeEach(() => {
      // Mock the DOM element that the method tries to access
      mockSelectElement = {
        value: ''
      } as HTMLSelectElement;
      spyOn(document, 'getElementById').and.returnValue(mockSelectElement);
    });

    it('should call lockUnlock with false for unlock operation', () => {
      // Arrange
      const mockEvent = { target: { value: DeviceListOperations.UNLOCK_DEVICES } };
      spyOn(component as any, 'lockUnlock');
      component.deviceOperations.length = 2;
      // Act
      component.changeDeviceOperation(mockEvent);

      // Assert
      expect((component as any).lockUnlock).toHaveBeenCalledWith(false);
      expect(mockSelectElement.value).toBe(DeviceListOperations.DeviceOperations);
    });

    it('should call lockUnlock with true for lock operation', () => {
      // Arrange
      const mockEvent = { target: { value: DeviceListOperations.LOCK_DEVICES } };
      spyOn(component as any, 'lockUnlock');
      component.deviceOperations.length = 2;
      // Act
      component.changeDeviceOperation(mockEvent);

      // Assert
      expect((component as any).lockUnlock).toHaveBeenCalledWith(true);
      expect(mockSelectElement.value).toBe(DeviceListOperations.DeviceOperations);
    });

    it('should call convertDataToTest for test device operation', () => {
      // Arrange
      const mockEvent = { target: { value: DeviceListOperations.SET_DEVICE_TO_TEST } };
      spyOn(component as any, 'convertDataToTest');
      component.deviceOperations.length = 2;
      // Act
      component.changeDeviceOperation(mockEvent);

      // Assert
      expect((component as any).convertDataToTest).toHaveBeenCalled();
      expect(mockSelectElement.value).toBe(DeviceListOperations.DeviceOperations);
    });

    it('should call convertDataToClient for client device operation', () => {
      // Arrange
      const mockEvent = { target: { value: DeviceListOperations.SET_DEVICE_TO_CLIENT } };
      spyOn(component as any, 'convertDataToClient');
      component.deviceOperations.length = 2;
      // Act
      component.changeDeviceOperation(mockEvent);

      // Assert
      expect((component as any).convertDataToClient).toHaveBeenCalled();
      expect(mockSelectElement.value).toBe(DeviceListOperations.DeviceOperations);
    });

    it('should call convertDataToDemo for demo device operation', () => {
      // Arrange
      const mockEvent = { target: { value: DeviceListOperations.SET_DEVICE_TO_DEMO } };
      spyOn(component as any, 'convertDataToDemo');
      component.deviceOperations.length = 2;
      // Act
      component.changeDeviceOperation(mockEvent);

      // Assert
      expect((component as any).convertDataToDemo).toHaveBeenCalled();
      expect(mockSelectElement.value).toBe(DeviceListOperations.DeviceOperations);
    });

    it('should call associationDeviceWithSalesOrder for customer association operation', () => {
      // Arrange
      const mockEvent = { target: { value: DeviceListOperations.CUSTOMER_SALES_ORDER_ASSOCIATION } };
      spyOn(component as any, 'associationDeviceWithSalesOrder');
      component.deviceOperations.length = 2;
      // Act
      component.changeDeviceOperation(mockEvent);

      // Assert
      expect((component as any).associationDeviceWithSalesOrder).toHaveBeenCalled();
      expect(mockSelectElement.value).toBe(DeviceListOperations.DeviceOperations);
    });

    it('should call enableDisableDevice with true for edit enable operation', () => {
      // Arrange
      const mockEvent = { target: { value: DeviceListOperations.EDIT_ENABLE_DEVICE } };
      spyOn(component as any, 'enableDisableDevice');
      component.deviceOperations.length = 2;
      // Act
      component.changeDeviceOperation(mockEvent);

      // Assert
      expect((component as any).enableDisableDevice).toHaveBeenCalledWith(true);
      expect(mockSelectElement.value).toBe(DeviceListOperations.DeviceOperations);
    });

    it('should call enableDisableDevice with false for edit disable operation', () => {
      // Arrange
      const mockEvent = { target: { value: DeviceListOperations.EDIT_DISABLE_DEVICE } };
      spyOn(component as any, 'enableDisableDevice');
      component.deviceOperations.length = 2;
      // Act
      component.changeDeviceOperation(mockEvent);

      // Assert
      expect((component as any).enableDisableDevice).toHaveBeenCalledWith(false);
      expect(mockSelectElement.value).toBe(DeviceListOperations.DeviceOperations);
    });

    it('should call exportCSV for export operation', () => {
      // Arrange
      const mockEvent = { target: { value: DeviceListOperations.Export_CSV } };
      spyOn(component, 'exportCSV');
      component.deviceOperations.length = 2;
      // Act
      component.changeDeviceOperation(mockEvent);

      // Assert
      expect(component.exportCSV).toHaveBeenCalled();
      expect(mockSelectElement.value).toBe(DeviceListOperations.DeviceOperations);
    });

    it('should call validateProductStatusForRMAAction for RMA operation', () => {
      // Arrange
      const mockEvent = { target: { value: DeviceListOperations.RMA_DEVICES } };
      spyOn(component as any, 'validateProductStatusForRMAAction');
      component.deviceOperations.length = 2;
      // Act
      component.changeDeviceOperation(mockEvent);

      // Assert
      expect((component as any).validateProductStatusForRMAAction).toHaveBeenCalled();
      expect(mockSelectElement.value).toBe(DeviceListOperations.DeviceOperations);
    });

    it('should call validateProductStatusForDisableAction for disable operation', () => {
      // Arrange
      const mockEvent = { target: { value: DeviceListOperations.DISABLED_DEVICES } };
      spyOn(component as any, 'validateProductStatusForDisableAction');
      component.deviceOperations.length = 2;
      // Act
      component.changeDeviceOperation(mockEvent);

      // Assert
      expect((component as any).validateProductStatusForDisableAction).toHaveBeenCalled();
      expect(mockSelectElement.value).toBe(DeviceListOperations.DeviceOperations);
    });

    it('should handle default case and reset dropdown', () => {
      // Arrange
      const mockEvent = { target: { value: 'UNKNOWN_OPERATION' } };

      // Act
      component.changeDeviceOperation(mockEvent);

      // Assert
      expect(document.getElementById).toHaveBeenCalledWith('deviceOperation');
      expect(mockSelectElement.value).toBe(DeviceListOperations.DeviceOperations);
    });

    it('should reset dropdown selection after any operation', () => {
      // Arrange
      const mockEvent = { target: { value: DeviceListOperations.LOCK_DEVICES } };
      spyOn(component as any, 'lockUnlock');

      // Act
      component.changeDeviceOperation(mockEvent);

      // Assert
      expect(document.getElementById).toHaveBeenCalledWith('deviceOperation');
      expect(mockSelectElement.value).toBe(DeviceListOperations.DeviceOperations);
    });
  });

  describe('onChangeDevice', () => {
    it('should add device to selection when checked', () => {
      // Arrange
      const mockDevice = deviceList[0];
      const mockEvent = { target: { checked: true } };
      component.deviceIdList = [];
      component.selectedDeviceList = [];
      spyOn(component, 'defaultSelectAll');

      // Act
      component.onChangeDevice(mockDevice, mockEvent);

      // Assert
      expect(component.deviceIdList).toContain(mockDevice.id);
      expect(component.selectedDeviceList).toContain(mockDevice);
      expect(component.defaultSelectAll).toHaveBeenCalled();
    });

    it('should remove device from selection when unchecked', () => {
      // Arrange
      const mockDevice = deviceList[0];
      component.deviceIdList = [mockDevice.id];
      component.selectedDeviceList = [mockDevice];
      const mockEvent = { target: { checked: false } };
      spyOn(component, 'defaultSelectAll');

      // Act
      component.onChangeDevice(mockDevice, mockEvent);

      // Assert
      expect(component.deviceIdList).not.toContain(mockDevice.id);
      expect(component.selectedDeviceList).not.toContain(mockDevice);
      expect(component.defaultSelectAll).toHaveBeenCalled();
    });
  });

  describe('convertDataToClient', () => {
    it('should convert devices to client when validation passes', async () => {
      // Arrange
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      deviceOperationServiceSpy.convertDevicesToClient.and.returnValue(Promise.resolve());
      spyOn(component, 'clearDeviceIdCheckBox');

      // Act
      await (component as any).convertDataToClient();

      // Assert
      expect(deviceOperationServiceSpy.convertDevicesToClient).toHaveBeenCalledWith([1, 2], deviceList.slice(0, 2));
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should not convert devices when validation fails', async () => {
      // Arrange
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(false);

      // Act
      await (component as any).convertDataToClient();

      // Assert
      expect(deviceOperationServiceSpy.convertDevicesToClient).not.toHaveBeenCalled();
    });

  });

  describe('convertDataToDemo', () => {
    it('should convert devices to demo when validation passes', async () => {
      // Arrange
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      deviceOperationServiceSpy.convertDevicesToDemo.and.returnValue(Promise.resolve());
      spyOn(component, 'clearDeviceIdCheckBox');

      // Act
      await (component as any).convertDataToDemo();

      // Assert
      expect(deviceOperationServiceSpy.convertDevicesToDemo).toHaveBeenCalledWith([1, 2], deviceList.slice(0, 2));
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should not convert devices when validation fails', async () => {
      // Arrange
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(false);

      // Act
      await (component as any).convertDataToDemo();

      // Assert
      expect(deviceOperationServiceSpy.convertDevicesToDemo).not.toHaveBeenCalled();
    });

  });

  describe('convertDataToTest', () => {
    it('should convert devices to test when validation passes', async () => {
      // Arrange
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      deviceOperationServiceSpy.convertDevicesToTest.and.returnValue(Promise.resolve());
      spyOn(component, 'clearDeviceIdCheckBox');

      // Act
      await (component as any).convertDataToTest();

      // Assert
      expect(deviceOperationServiceSpy.convertDevicesToTest).toHaveBeenCalledWith([1, 2], deviceList.slice(0, 2));
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should not convert devices when validation fails', async () => {
      // Arrange
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(false);

      // Act
      await (component as any).convertDataToTest();

      // Assert
      expect(deviceOperationServiceSpy.convertDevicesToTest).not.toHaveBeenCalled();
    });

  });

  describe('validateProductStatusForDisableAction', () => {
    it('should disable devices when validation passes and user confirms', async () => {
      // Arrange
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      deviceOperationServiceSpy.disableProductStatusForDevices.and.returnValue(Promise.resolve());
      spyOn(component, 'clearDeviceIdCheckBox');

      const mockConfirmDialogService = TestBed.inject(ConfirmDialogService);
      spyOn(mockConfirmDialogService, 'getBasicModelConfigForDisableAction').and.returnValue({
        title: 'Confirm',
        message: 'Are you sure?',
        btnOkText: 'Yes',
        btnCancelText: 'No'
      });
      spyOn(mockConfirmDialogService, 'confirm').and.returnValue(Promise.resolve(true));

      // Act
      await (component as any).validateProductStatusForDisableAction();

      // Assert
      expect(deviceOperationServiceSpy.disableProductStatusForDevices).toHaveBeenCalledWith([1, 2]);
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should not disable devices when validation fails', async () => {
      // Arrange
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(false);

      // Act
      await (component as any).validateProductStatusForDisableAction();

      // Assert
      expect(deviceOperationServiceSpy.disableProductStatusForDevices).not.toHaveBeenCalled();
    });

    it('should not disable devices when user cancels confirmation', async () => {
      // Arrange
      component.deviceIdList = [1];
      component.selectedDeviceList = [deviceList[0]];
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);

      const mockConfirmDialogService = TestBed.inject(ConfirmDialogService);
      spyOn(mockConfirmDialogService, 'getBasicModelConfigForDisableAction').and.returnValue({
        title: 'Confirm',
        message: 'Are you sure?',
        btnOkText: 'Yes',
        btnCancelText: 'No'
      });
      spyOn(mockConfirmDialogService, 'confirm').and.returnValue(Promise.resolve(false));

      // Act
      await (component as any).validateProductStatusForDisableAction();

      // Assert
      expect(deviceOperationServiceSpy.disableProductStatusForDevices).not.toHaveBeenCalled();
    });
  });

  describe('validateProductStatusForRMAAction', () => {
    it('should mark devices as RMA when validation passes and user confirms', async () => {
      // Arrange
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      deviceOperationServiceSpy.rmaProductStatusForDevices.and.returnValue(Promise.resolve());
      spyOn(component, 'clearDeviceIdCheckBox');

      const mockConfirmDialogService = TestBed.inject(ConfirmDialogService);
      spyOn(mockConfirmDialogService, 'getBasicModelConfigForRMAAction').and.returnValue({
        title: 'Confirm RMA',
        message: 'Are you sure you want to mark as RMA?',
        btnOkText: 'Yes',
        btnCancelText: 'No'
      });
      spyOn(mockConfirmDialogService, 'confirm').and.returnValue(Promise.resolve(true));

      // Act
      await (component as any).validateProductStatusForRMAAction();

      // Assert
      expect(deviceOperationServiceSpy.rmaProductStatusForDevices).toHaveBeenCalledWith([1, 2]);
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should not mark devices as RMA when validation fails', async () => {
      // Arrange
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(false);

      // Act
      await (component as any).validateProductStatusForRMAAction();

      // Assert
      expect(deviceOperationServiceSpy.rmaProductStatusForDevices).not.toHaveBeenCalled();
    });

    it('should not mark devices as RMA when user cancels confirmation', async () => {
      // Arrange
      component.deviceIdList = [1];
      component.selectedDeviceList = [deviceList[0]];
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);

      const mockConfirmDialogService = TestBed.inject(ConfirmDialogService);
      spyOn(mockConfirmDialogService, 'getBasicModelConfigForRMAAction').and.returnValue({
        title: 'Confirm RMA',
        message: 'Are you sure you want to mark as RMA?',
        btnOkText: 'Yes',
        btnCancelText: 'No'
      });
      spyOn(mockConfirmDialogService, 'confirm').and.returnValue(Promise.resolve(false));

      // Act
      await (component as any).validateProductStatusForRMAAction();

      // Assert
      expect(deviceOperationServiceSpy.rmaProductStatusForDevices).not.toHaveBeenCalled();
    });
  });

  describe('exportCSV', () => {
    it('should export CSV successfully', async () => {
      // Arrange
      component.deviceIdList = [1, 2];
      deviceOperationServiceSpy.exportDeviceCSV.and.returnValue(Promise.resolve());
      spyOn(component, 'clearDeviceIdCheckBox');

      // Act
      await component.exportCSV();

      // Assert
      expect(deviceOperationServiceSpy.exportDeviceCSV).toHaveBeenCalledWith([1, 2]);
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

  });

  describe('clearDeviceIdCheckBox', () => {
    it('should clear device selection and uncheck all checkboxes', () => {
      // Arrange
      component.deviceIdList = [1, 2, 3];
      component.selectedDeviceList = deviceList;

      // Mock DOM elements
      const mockCheckboxes = [
        { checked: true },
        { checked: true },
        { checked: false }
      ];
      const mockSelectAllCheckbox = { checked: true };

      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);
      spyOn(document, 'getElementById').and.returnValue(mockSelectAllCheckbox as any);

      // Act
      component.clearDeviceIdCheckBox();

      // Assert
      expect(component.deviceIdList).toEqual([]);
      expect(component.selectedDeviceList).toEqual([]);
      expect(mockCheckboxes[0].checked).toBe(false);
      expect(mockCheckboxes[1].checked).toBe(false);
      expect(mockCheckboxes[2].checked).toBe(false);
      expect(mockSelectAllCheckbox.checked).toBe(false);
    });

    it('should handle case when select all checkbox does not exist', () => {
      // Arrange
      component.deviceIdList = [1];
      component.selectedDeviceList = [deviceList[0]];

      const mockCheckboxes = [{ checked: true }];

      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);
      spyOn(document, 'getElementById').and.returnValue(null);

      // Act
      component.clearDeviceIdCheckBox();

      // Assert
      expect(component.deviceIdList).toEqual([]);
      expect(component.selectedDeviceList).toEqual([]);
      expect(mockCheckboxes[0].checked).toBe(false);
    });
  });

  describe('defaultSelectAll', () => {
    it('should check select all checkbox when all devices are selected', () => {
      // Arrange
      component.localDeviceList = deviceList;
      component.deviceIdList = deviceList.map(d => d.id);

      const mockSelectAllCheckbox = { checked: false };
      spyOn(document, 'getElementById').and.returnValue(mockSelectAllCheckbox as any);

      // Act
      component.defaultSelectAll();

      // Assert
      expect(mockSelectAllCheckbox.checked).toBe(true);
    });

    it('should uncheck select all checkbox when not all devices are selected', () => {
      // Arrange
      component.localDeviceList = deviceList;
      component.deviceIdList = [deviceList[0].id]; // Only one device selected

      const mockSelectAllCheckbox = { checked: true };
      spyOn(document, 'getElementById').and.returnValue(mockSelectAllCheckbox as any);

      // Act
      component.defaultSelectAll();

      // Assert
      expect(mockSelectAllCheckbox.checked).toBe(false);
    });

    it('should uncheck select all checkbox when no devices are selected', () => {
      // Arrange
      component.localDeviceList = deviceList;
      component.deviceIdList = []; // No devices selected

      const mockSelectAllCheckbox = { checked: true };
      spyOn(document, 'getElementById').and.returnValue(mockSelectAllCheckbox as any);

      // Act
      component.defaultSelectAll();

      // Assert
      expect(mockSelectAllCheckbox.checked).toBe(false);
    });

    it('should handle case when select all checkbox does not exist', () => {
      // Arrange
      component.localDeviceList = deviceList;
      component.deviceIdList = deviceList.map(d => d.id);

      spyOn(document, 'getElementById').and.returnValue(null);

      // Act & Assert - should not throw error
      expect(() => component.defaultSelectAll()).not.toThrow();
    });
  });

  describe('selectAllDevice', () => {
    it('should select all devices when checkbox is checked', () => {
      // Arrange
      component.localDeviceList = deviceList;
      component.deviceIdList = [];
      component.selectedDeviceList = [];
      const mockEvent = { target: { checked: true } };

      const mockCheckboxes = [
        { checked: false },
        { checked: false },
        { checked: false }
      ];
      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);

      // Act
      component.selectAllDevice(mockEvent);

      // Assert
      expect(component.deviceIdList).toEqual(deviceList.map(d => d.id));
      expect(component.selectedDeviceList).toEqual(deviceList);
      expect(mockCheckboxes[0].checked).toBe(true);
      expect(mockCheckboxes[1].checked).toBe(true);
      expect(mockCheckboxes[2].checked).toBe(true);
    });

    it('should deselect all devices when checkbox is unchecked', () => {
      // Arrange
      component.localDeviceList = deviceList;
      component.deviceIdList = deviceList.map(d => d.id);
      component.selectedDeviceList = [...deviceList];
      const mockEvent = { target: { checked: false } };

      const mockCheckboxes = [
        { checked: true },
        { checked: true },
        { checked: true }
      ];
      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);

      // Act
      component.selectAllDevice(mockEvent);

      // Assert
      expect(component.deviceIdList).toEqual([]);
      expect(component.selectedDeviceList).toEqual([]);
      expect(mockCheckboxes[0].checked).toBe(false);
      expect(mockCheckboxes[1].checked).toBe(false);
      expect(mockCheckboxes[2].checked).toBe(false);
    });

    it('should handle selecting all when some devices are already selected', () => {
      // Arrange
      component.localDeviceList = deviceList;
      component.deviceIdList = [deviceList[0].id]; // Only first device selected
      component.selectedDeviceList = [deviceList[0]];
      const mockEvent = { target: { checked: true } };

      const mockCheckboxes = [
        { checked: true },
        { checked: false },
        { checked: false }
      ];
      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);

      // Act
      component.selectAllDevice(mockEvent);

      // Assert
      expect(component.deviceIdList).toEqual(deviceList.map(d => d.id));
      expect(component.selectedDeviceList).toEqual(deviceList);
      expect(mockCheckboxes[0].checked).toBe(true);
      expect(mockCheckboxes[1].checked).toBe(true);
      expect(mockCheckboxes[2].checked).toBe(true);
    });
  });

  describe('defaultSelectDevice', () => {
    it('should return true when device is selected', () => {
      // Arrange
      component.deviceIdList = [63, 64];

      // Act
      const result = component.defaultSelectDevice(63);

      // Assert
      expect(result).toBe(true);
    });

    it('should return false when device is not selected', () => {
      // Arrange
      component.deviceIdList = [63, 64];

      // Act
      const result = component.defaultSelectDevice(65);

      // Assert
      expect(result).toBe(false);
    });

    it('should return false when no devices are selected', () => {
      // Arrange
      component.deviceIdList = [];

      // Act
      const result = component.defaultSelectDevice(63);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('filterPageSubjectCallForReloadPage', () => {
    it('should call device operation service with correct parameters', () => {
      // Arrange
      const clearFilter = true;
      const clearSort = false;
      component.deviceSearchRequestBody = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);

      // Act
      component.filterPageSubjectCallForReloadPage(clearFilter, clearSort);

      // Assert
      expect(deviceOperationServiceSpy.callRefreshPageSubject).toHaveBeenCalledWith(
        jasmine.objectContaining({
          isReloadData: true,
          isDefaultPageNumber: true,
          isClearFilter: false,
          isMovePrevPage: false,
          isOtherAction: false
        }),
        'deviceListResource',
        jasmine.any(Boolean),
        jasmine.any(DeviceSearchRequest) // or objectContaining if you want to match specific fields
      );
    });

    it('should call device operation service with different parameters', () => {
      // Arrange
      const clearFilter = false;
      const clearSort = true;
      component.deviceSearchRequestBody = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);

      // Act
      component.filterPageSubjectCallForReloadPage(clearFilter, clearSort);

      // Assert
      expect(deviceOperationServiceSpy.callRefreshPageSubject).toHaveBeenCalledWith(
        jasmine.objectContaining({
          isReloadData: true,
          isDefaultPageNumber: clearFilter,
          isClearFilter: clearSort
        }),
        'deviceListResource',
        jasmine.any(Boolean),
        component.deviceSearchRequestBody
      );
    });
  });

  describe('clickOnRefreshButton', () => {
    it('should update filter cache and call refresh filter', () => {
      // Arrange
      spyOn(component, 'updateFilterCacheInBackground');
      spyOn(component, 'refreshFilter');

      // Act
      component.clickOnRefreshButton();

      // Assert
      expect(component.updateFilterCacheInBackground).toHaveBeenCalled();
      expect(component.refreshFilter).toHaveBeenCalled();
    });
  });

  describe('associationDeviceWithSalesOrder', () => {
    it('should call device operation service when validation passes and user confirms', async () => {
      // Arrange
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      deviceOperationServiceSpy.associateDevicesWithSalesOrder.and.returnValue(Promise.resolve());
      spyOn(component, 'clearDeviceIdCheckBox');

      // Mock customer association service
      customerAssociationServiceSpy.openCustomerAssociationPopup.and.returnValue(Promise.resolve({
        button: true,
        basicSalesOrderDetailResponse: mockBasicSalesOrderDetailResponse,
        isSalesOrderNewAdd: false
      }));

      // Act
      await (component as any).associationDeviceWithSalesOrder();

      // Assert
      expect(deviceOperationServiceSpy.associateDevicesWithSalesOrder).toHaveBeenCalledWith([1, 2], mockBasicSalesOrderDetailResponse, false);
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should not call service when validation fails', async () => {
      // Arrange
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(false);

      // Act
      await (component as any).associationDeviceWithSalesOrder();

      // Assert
      expect(deviceOperationServiceSpy.associateDevicesWithSalesOrder).not.toHaveBeenCalled();
    });

    it('should not call service when user cancels dialog', async () => {
      // Arrange
      component.deviceIdList = [1];
      component.selectedDeviceList = [deviceList[0]];
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);

      // Mock customer association service to return cancelled dialog
      customerAssociationServiceSpy.openCustomerAssociationPopup.and.returnValue(Promise.resolve({
        button: false,
        basicSalesOrderDetailResponse: null,
        isSalesOrderNewAdd: false
      }));

      // Act
      await (component as any).associationDeviceWithSalesOrder();

      // Assert
      expect(deviceOperationServiceSpy.associateDevicesWithSalesOrder).not.toHaveBeenCalled();
    });

    it('should handle dialog error gracefully', async () => {
      // Arrange
      component.deviceIdList = [1];
      component.selectedDeviceList = [deviceList[0]];
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);

      // Mock customer association service to throw error
      customerAssociationServiceSpy.openCustomerAssociationPopup.and.returnValue(Promise.reject(new Error('Dialog Error')));

      // Act
      await (component as any).associationDeviceWithSalesOrder();

      // Assert
      expect(deviceOperationServiceSpy.associateDevicesWithSalesOrder).not.toHaveBeenCalled();
    });
  });

  describe('refreshFilter', () => {
    it('should reset page and call filter page subject for reload', () => {
      // Arrange
      component.page = 5;
      component.previousPage = 3;
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      // Act
      component.refreshFilter();

      // Assert
      expect(component.page).toBe(0);
      expect(component.previousPage).toBe(1);
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, true);
    });
  });

  describe('updateFilterCacheInBackground', () => {
    it('should call device operation service to update cache', () => {
      // Act
      component.updateFilterCacheInBackground();

      // Assert
      expect(deviceOperationServiceSpy.updateCacheInBackground).toHaveBeenCalled();
    });
  });

  describe('updateSalesOrderCacheOnly', () => {
    it('should call device operation service to update sales order cache only', () => {
      // Act
      component.updateSalesOrderCacheOnly();

      // Assert
      expect(deviceOperationServiceSpy.updateSalesOrderCacheOnly).toHaveBeenCalled();
    });
  });

  describe('deviceDetailModel', () => {
    it('should set device ID and show device detail view', () => {
      // Arrange
      const deviceId = 123;
      component.displayDevice = true;
      component.displayDeviceDetail = false;

      // Act
      component.deviceDetailModel(deviceId);

      // Assert
      expect(component.deviceIdInput).toBe(deviceId);
      expect(component.displayDevice).toBe(false);
      expect(component.displayDeviceDetail).toBe(true);
    });
  });

  describe('showDevice', () => {
    it('should show device listing and update cache', () => {
      // Arrange
      component.isFilterComponentInitWithApicall = true;
      component.listPageRefreshForbackToDetailPage = false;
      component.displayDevice = false;
      component.displayDeviceDetail = true;
      component.deviceIdList = [1, 2, 3];
      component.selectedDeviceList = deviceList;
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      // Act
      component.showDevice();

      // Assert
      expect(component.isFilterComponentInitWithApicall).toBe(false);
      expect(component.listPageRefreshForbackToDetailPage).toBe(true);
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(false, false);
      expect(component.deviceIdList).toEqual([]);
      // selectedDeviceList is not cleared in the actual implementation
      expect(component.selectedDeviceList).toEqual(deviceList);
      expect(component.displayDevice).toBe(true);
      expect(component.displayDeviceDetail).toBe(false);
    });
  });

  describe('toggleFilter', () => {
    it('should toggle filter visibility and update button text', () => {
      // Arrange
      component.isFilterComponentInitWithApicall = true;
      component.listPageRefreshForbackToDetailPage = true;
      component.isFilterHidden = false;
      component.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
      // Act
      component.toggleFilter();

      // Assert
      expect(component.isFilterComponentInitWithApicall).toBe(false);
      expect(component.listPageRefreshForbackToDetailPage).toBe(false);
      expect(component.isFilterHidden).toBe(true);
      expect(component.hideShowFilterButtonText).toBe(collapseFilterTextEnum.SHOW_FILTER);
    });

    it('should toggle filter visibility from hidden to visible', () => {
      // Arrange
      component.isFilterHidden = true;
      component.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
      // Act
      component.toggleFilter();

      // Assert
      expect(component.isFilterHidden).toBe(false);
      expect(component.hideShowFilterButtonText).toBe(collapseFilterTextEnum.HIDE_FILTER);
    });
  });

  describe('lockUnlock', () => {
    it('should lock devices when validation passes', async () => {
      // Arrange
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      deviceOperationServiceSpy.lockUnlockDevices.and.returnValue(Promise.resolve());
      spyOn(component, 'clearDeviceIdCheckBox');

      // Act
      await (component as any).lockUnlock(true);

      // Assert
      expect(deviceOperationServiceSpy.lockUnlockDevices).toHaveBeenCalledWith([1, 2], true);
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should unlock devices when validation passes', async () => {
      // Arrange
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      deviceOperationServiceSpy.lockUnlockDevices.and.returnValue(Promise.resolve());
      spyOn(component, 'clearDeviceIdCheckBox');

      // Act
      await (component as any).lockUnlock(false);

      // Assert
      expect(deviceOperationServiceSpy.lockUnlockDevices).toHaveBeenCalledWith([1, 2], false);
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should clear checkboxes and return when validation fails', async () => {
      // Arrange
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(false);
      spyOn(component, 'clearDeviceIdCheckBox');

      // Act
      await (component as any).lockUnlock(true);

      // Assert
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
      expect(deviceOperationServiceSpy.lockUnlockDevices).not.toHaveBeenCalled();
    });

    it('should handle lock/unlock error gracefully', async () => {
      // Arrange
      component.deviceIdList = [1];
      component.selectedDeviceList = [deviceList[0]];
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      spyOn(component, 'clearDeviceIdCheckBox');

      // Act
      await (component as any).lockUnlock(true);

      // Assert
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });
  });

  describe('enableDisableDevice', () => {
    it('should enable devices when validation passes', async () => {
      // Arrange
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateUserCountryAccess.and.returnValue(true);
      deviceOperationServiceSpy.enableDisableDevices.and.returnValue(Promise.resolve());
      spyOn(component, 'clearDeviceIdCheckBox');

      // Act
      await (component as any).enableDisableDevice(true);

      // Assert
      expect(deviceOperationServiceSpy.enableDisableDevices).toHaveBeenCalledWith([1, 2], true);
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should disable devices when validation passes', async () => {
      // Arrange
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateUserCountryAccess.and.returnValue(true);
      deviceOperationServiceSpy.enableDisableDevices.and.returnValue(Promise.resolve());
      spyOn(component, 'clearDeviceIdCheckBox');

      // Act
      await (component as any).enableDisableDevice(false);

      // Assert
      expect(deviceOperationServiceSpy.enableDisableDevices).toHaveBeenCalledWith([1, 2], false);
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should clear checkboxes and return when validation fails', async () => {
      // Arrange
      deviceOperationServiceSpy.validateUserCountryAccess.and.returnValue(false);
      spyOn(component, 'clearDeviceIdCheckBox');

      // Act
      await (component as any).enableDisableDevice(true);

      // Assert
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
      expect(deviceOperationServiceSpy.enableDisableDevices).not.toHaveBeenCalled();
    });

  });

  describe('ngOnDestroy', () => {
    it('should unsubscribe from subscriptions when they exist', () => {
      // Arrange
      const mockSubscription1 = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      const mockSubscription2 = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component.subscriptionForCommonloading = mockSubscription1;
      component.subscriptionForDeviceListFilterRequestParameter = mockSubscription2;

      // Act
      component.ngOnDestroy();

      // Assert
      expect(mockSubscription1.unsubscribe).toHaveBeenCalled();
      expect(mockSubscription2.unsubscribe).toHaveBeenCalled();
    });

    it('should handle undefined subscriptions gracefully', () => {
      // Arrange
      component.subscriptionForCommonloading = undefined;
      component.subscriptionForDeviceListFilterRequestParameter = undefined;

      // Act & Assert - should not throw error
      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });

  describe('subjectInit', () => {
    it('should handle device filter action with reload data and default page number', () => {
      // Arrange
      const mockDeviceFilterAction: DeviceFilterAction = {
        listingPageReloadSubjectParameter: {
          isReloadData: true,
          isDefaultPageNumber: true,
          isClearFilter: false,
          isMovePrevPage: false,
          isOtherAction: false
        },
        deviceSearchRequest: new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null)
      };

      component.deviceIdList = [1, 2, 3];
      component.selectedDeviceList = deviceList;
      spyOn(component, 'loadAll');
      spyOn(component as any, 'resetPage');

      // Act
      (component as any).subjectInit();

      // Simulate the subject emission
      const deviceFilterSubject = deviceOperationServiceSpy.getDeviceListFilterRequestParameterSubject();
      deviceFilterSubject.next(mockDeviceFilterAction);

      // Assert
      expect(component.deviceIdList).toEqual([]);
      expect(component.selectedDeviceList).toEqual([]);
      expect((component as any).resetPage).toHaveBeenCalled();
      expect(component.loadAll).toHaveBeenCalledWith(mockDeviceFilterAction.deviceSearchRequest);
    });

    it('should handle device filter action with reload data but not default page number', () => {
      // Arrange
      const mockDeviceFilterAction: DeviceFilterAction = {
        listingPageReloadSubjectParameter: {
          isReloadData: true,
          isDefaultPageNumber: false,
          isClearFilter: false,
          isMovePrevPage: false,
          isOtherAction: false
        },
        deviceSearchRequest: new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null)
      };

      component.deviceIdList = [1, 2, 3];
      component.selectedDeviceList = deviceList;
      spyOn(component, 'loadAll');
      spyOn(component as any, 'resetPage');

      // Act
      (component as any).subjectInit();

      // Simulate the subject emission
      const deviceFilterSubject = deviceOperationServiceSpy.getDeviceListFilterRequestParameterSubject();
      deviceFilterSubject.next(mockDeviceFilterAction);

      // Assert
      expect(component.deviceIdList).toEqual([1, 2, 3]); // Should not be cleared
      expect(component.selectedDeviceList).toEqual(deviceList); // Should not be cleared
      expect((component as any).resetPage).not.toHaveBeenCalled();
      expect(component.loadAll).toHaveBeenCalledWith(mockDeviceFilterAction.deviceSearchRequest);
    });

    it('should not load data when isReloadData is false', () => {
      // Arrange
      const mockDeviceFilterAction: DeviceFilterAction = {
        listingPageReloadSubjectParameter: {
          isReloadData: false,
          isDefaultPageNumber: true,
          isClearFilter: false,
          isMovePrevPage: false,
          isOtherAction: false
        },
        deviceSearchRequest: new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null)
      };

      spyOn(component, 'loadAll');

      // Act
      (component as any).subjectInit();

      // Simulate the subject emission
      const deviceFilterSubject = deviceOperationServiceSpy.getDeviceListFilterRequestParameterSubject();
      deviceFilterSubject.next(mockDeviceFilterAction);

      // Assert
      expect(component.loadAll).not.toHaveBeenCalled();
    });
  });

  describe('setLoadingStatus', () => {
    it('should set loading status to true', () => {
      // Arrange
      component.loading = false;

      // Act
      (component as any).setLoadingStatus(true);

      // Assert
      expect(component.loading).toBe(true);
    });

    it('should set loading status to false', () => {
      // Arrange
      component.loading = true;

      // Act
      (component as any).setLoadingStatus(false);

      // Assert
      expect(component.loading).toBe(false);
    });
  });

  describe('resetPage', () => {
    it('should reset page and previousPage to initial values', () => {
      // Arrange
      component.page = 5;
      component.previousPage = 3;

      // Act
      (component as any).resetPage();

      // Assert
      expect(component.page).toBe(0);
      expect(component.previousPage).toBe(1);
    });
  });

  describe('getDeviceData', () => {
    it('should initialize device data with default values', () => {
      // Arrange
      component.page = 5;
      component.isFilterHidden = true;
      component.isFilterComponentInitWithApicall = false;
      component.listPageRefreshForbackToDetailPage = true;
      component.displayDevice = false;
      component.displayDeviceDetail = true;
      spyOn(component, 'clearDeviceIdCheckBox');

      // Act
      component.getDeviceData();

      // Assert
      expect(component.page).toBe(0);
      expect(component.drpselectsize).toBe(ITEMS_PER_PAGE);
      expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE);
      expect(component.isFilterHidden).toBe(false);
      expect(component.isFilterComponentInitWithApicall).toBe(true);
      expect(component.listPageRefreshForbackToDetailPage).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
      expect(component.displayDevice).toBe(true);
      expect(component.displayDeviceDetail).toBe(false);
    });
  });

  describe('selectAllDevice edge cases', () => {
    it('should handle selecting all when deviceIdList contains devices not in localDeviceList', () => {
      // Arrange
      component.localDeviceList = deviceList.slice(0, 2); // Only first 2 devices
      component.deviceIdList = [deviceList[2].id]; // Third device ID
      component.selectedDeviceList = [deviceList[2]]; // Third device
      const mockEvent = { target: { checked: true } };

      const mockCheckboxes = [
        { checked: false },
        { checked: false }
      ];
      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);

      // Act
      component.selectAllDevice(mockEvent);

      // Assert
      expect(component.deviceIdList).toEqual([deviceList[2].id, deviceList[0].id, deviceList[1].id]);
      expect(component.selectedDeviceList).toEqual([deviceList[2], deviceList[0], deviceList[1]]);
      expect(mockCheckboxes[0].checked).toBe(true);
      expect(mockCheckboxes[1].checked).toBe(true);
    });

    it('should handle deselecting all when some devices are not in localDeviceList', () => {
      // Arrange
      component.localDeviceList = deviceList.slice(0, 2); // Only first 2 devices
      component.deviceIdList = [deviceList[0].id, deviceList[1].id, deviceList[2].id]; // All device IDs
      component.selectedDeviceList = [...deviceList]; // All devices
      const mockEvent = { target: { checked: false } };

      const mockCheckboxes = [
        { checked: true },
        { checked: true }
      ];
      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);

      // Act
      component.selectAllDevice(mockEvent);

      // Assert
      expect(component.deviceIdList).toEqual([deviceList[2].id]); // Only third device remains
      expect(component.selectedDeviceList).toEqual([deviceList[2]]); // Only third device remains
      expect(mockCheckboxes[0].checked).toBe(false);
      expect(mockCheckboxes[1].checked).toBe(false);
    });
  });

  describe('defaultSelectAll edge cases', () => {
    it('should handle empty localDeviceList', () => {
      // Arrange
      component.localDeviceList = [];
      component.deviceIdList = [1, 2, 3];

      const mockSelectAllCheckbox = { checked: false };
      spyOn(document, 'getElementById').and.returnValue(mockSelectAllCheckbox as any);

      // Act
      component.defaultSelectAll();

      // Assert
      expect(mockSelectAllCheckbox.checked).toBe(false); // Should be false for empty list
    });

    it('should break loop early when device not found', () => {
      // Arrange
      component.localDeviceList = deviceList;
      component.deviceIdList = [deviceList[0].id]; // Only first device selected

      const mockSelectAllCheckbox = { checked: true };
      spyOn(document, 'getElementById').and.returnValue(mockSelectAllCheckbox as any);

      // Act
      component.defaultSelectAll();

      // Assert
      expect(mockSelectAllCheckbox.checked).toBe(false);
    });
  });

  describe('Variable Update Methods', () => {
    it('should update isFilterComponentInitWithApicall and emit change', () => {
      // Arrange
      spyOn(component.isFilterComponentInitWithApicallChange, 'emit');
      const testValue = true;

      // Act
      component.updateIsFilterComponentInitWithApicall(testValue);

      // Assert
      expect(component.isFilterComponentInitWithApicall).toBe(testValue);
      expect(component.isFilterComponentInitWithApicallChange.emit).toHaveBeenCalledWith(testValue);
    });

    it('should update listPageRefreshForbackToDetailPage and emit change', () => {
      // Arrange
      spyOn(component.listPageRefreshForbackToDetailPageChange, 'emit');
      const testValue = true;

      // Act
      component.updateListPageRefreshForbackToDetailPage(testValue);

      // Assert
      expect(component.listPageRefreshForbackToDetailPage).toBe(testValue);
      expect(component.listPageRefreshForbackToDetailPageChange.emit).toHaveBeenCalledWith(testValue);
    });

    it('should update deviceSearchRequestBody and emit change', () => {
      // Arrange
      spyOn(component.deviceSearchRequestBodyChange, 'emit');
      const testValue = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

      // Act
      component.updateDeviceSearchRequestBody(testValue);

      // Assert
      expect(component.deviceSearchRequestBody).toBe(testValue);
      expect(component.deviceSearchRequestBodyChange.emit).toHaveBeenCalledWith(testValue);
    });

    it('should update isFilterHidden and emit change', () => {
      // Arrange
      spyOn(component.isFilterHiddenChange, 'emit');
      const testValue = true;

      // Act
      component.updateIsFilterHidden(testValue);

      // Assert
      expect(component.isFilterHidden).toBe(testValue);
      expect(component.isFilterHiddenChange.emit).toHaveBeenCalledWith(testValue);
    });
  });

  describe('toggleFilter with update methods', () => {
    it('should use update methods when toggling filter', () => {
      // Arrange
      component.isFilterHidden = false;
      spyOn(component, 'updateIsFilterComponentInitWithApicall');
      spyOn(component, 'updateListPageRefreshForbackToDetailPage');
      spyOn(component, 'updateIsFilterHidden');

      // Act
      component.toggleFilter();

      // Assert
      expect(component.updateIsFilterComponentInitWithApicall).toHaveBeenCalledWith(false);
      expect(component.updateListPageRefreshForbackToDetailPage).toHaveBeenCalledWith(false);
      expect(component.updateIsFilterHidden).toHaveBeenCalledWith(true);
      expect(component.hideShowFilterButtonText).toBe(collapseFilterTextEnum.SHOW_FILTER);
    });
  });

  describe('getDeviceData with update methods', () => {
    it('should use update methods when initializing device data', () => {
      // Arrange
      spyOn(component, 'updateIsFilterHidden');
      spyOn(component, 'updateIsFilterComponentInitWithApicall');
      spyOn(component, 'updateListPageRefreshForbackToDetailPage');
      spyOn(component, 'clearDeviceIdCheckBox');

      // Act
      component.getDeviceData();

      // Assert
      expect(component.updateIsFilterHidden).toHaveBeenCalledWith(false);
      expect(component.updateIsFilterComponentInitWithApicall).toHaveBeenCalledWith(true);
      expect(component.updateListPageRefreshForbackToDetailPage).toHaveBeenCalledWith(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });
  });

  describe('loadAll with update methods', () => {
    it('should use update method when loading device data', async () => {
      // Arrange
      const mockDeviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);
      spyOn(component, 'updateDeviceSearchRequestBody');
      spyOn(component, 'setLoadingStatus');

      const mockResult = {
        success: true,
        devices: [],
        totalDeviceDisplay: 0,
        totalDevice: 0,
        localDeviceList: [],
        totalItems: 0
      };
      spyOn(deviceOperationServiceSpy, 'loadDeviceList').and.returnValue(Promise.resolve(mockResult));

      // Act
      await component.loadAll(mockDeviceSearchRequest);

      // Assert
      expect(component.updateDeviceSearchRequestBody).toHaveBeenCalledWith(mockDeviceSearchRequest);
    });
  });

});